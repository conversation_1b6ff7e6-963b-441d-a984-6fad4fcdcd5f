"""
Select Area Page for the WOSS Seismic Analysis Tool.

This module handles the UI rendering for selecting the area of interest.
It follows the principles outlined in rules.md, particularly regarding
the separation of concerns between UI and backend logic.
"""

import streamlit as st
import numpy as np
import pandas as pd
import logging

# Import common modules
from common.constants import APP_TITLE
from common.session_state import initialize_session_state, reset_state
from common.ui_elements import get_suggested_batch_size

# Import utility functions
from utils.data_utils import get_surfaces, get_well_marker_pairs # Corrected imports
from utils.data_utils import get_nearest_trace_index, load_trace_sample
from utils.general_utils import parse_polyline_string, find_traces_near_polyline

def render(trace_selection=False):
    """Render the select area page UI."""
    initialize_session_state()

    if not trace_selection:
        st.header("Step 3: Select Analysis Mode")
        st.sidebar.header("Mode Selection")

        # Check if data is loaded
        if not st.session_state.header_loader:
            st.warning("Please load data first.")
            st.session_state.current_step = "load_data"
            st.rerun()
            return

        # Define a callback for mode selection changes
        def on_mode_change():
            # Get the current mode from the widget state
            current_mode = st.session_state.get(f"mode_selector_{st.session_state.get('mode_selector_counter', 0)}", "By well markers")
            
            # Log the mode change
            logging.info(f"Mode changed to: {current_mode}")
            
            # Update the selection_mode and mode_selector in session state
            st.session_state.selection_mode = current_mode
            st.session_state.mode_selector = current_mode
            
            # Reset area_selected flag when mode changes
            st.session_state.area_selected = False
            
            # Clear any previously selected data for precompute
            st.session_state.selected_data_for_precompute = None
            
            # Increment the counter to force a new widget instance on next render
            st.session_state.mode_selector_counter = st.session_state.get('mode_selector_counter', 0) + 1
            
            # Set a flag to force rerun
            st.session_state.force_mode_rerun = True
            
            # Log the state after update
            logging.info(f"After on_mode_change - selection_mode: {st.session_state.selection_mode}")
            logging.info(f"After on_mode_change - mode_selector: {st.session_state.mode_selector}")

        # Mode Selection with a unique key and on_change callback
        mode_options = [
            "By well markers",
            "Single inline (all crosslines)",
            "Single crossline (all inlines)",
            "By inline/crossline section (AOI)",
            "By Polyline File Import"
        ]

        # Initialize mode_selector if not already in session state
        if 'mode_selector' not in st.session_state:
            st.session_state.mode_selector = st.session_state.selection_mode or mode_options[0]
            st.session_state.selection_mode = st.session_state.mode_selector  # Ensure they're in sync

        # Create the selectbox with on_change callback
        # Handle the case when mode_selector is None by defaulting to the first option
        mode_index = 0  # Default to first option
        if st.session_state.mode_selector is not None and st.session_state.mode_selector in mode_options:
            mode_index = mode_options.index(st.session_state.mode_selector)

        # Use a unique key for the selectbox to ensure it updates properly
        selectbox_key = f"mode_selector_{st.session_state.get('mode_selector_counter', 0)}"
        
        selected_mode = st.sidebar.selectbox(
            "Select Mode",
            options=mode_options,
            index=mode_index,
            key=selectbox_key,
            on_change=on_mode_change
        )

        # Ensure selection_mode is set and trigger a rerun if it changed
        if st.session_state.selection_mode != selected_mode:
            st.session_state.selection_mode = selected_mode
            st.session_state.mode_selector = selected_mode
            # Force a rerun to ensure the UI updates
            st.session_state.force_mode_rerun = True
            st.rerun()
            
        # Log the current state for debugging
        logging.info(f"Current selection_mode: {st.session_state.selection_mode}")
        logging.info(f"Current mode_selector: {st.session_state.mode_selector}")

        # Check if we need to force a rerun due to mode change
        if st.session_state.get('force_mode_rerun', False):
            # Clear the flag
            st.session_state.force_mode_rerun = False
            # Rerun to update the UI based on the new mode
            st.rerun()

        # Well Markers Mode
        if st.session_state.selection_mode == "By well markers":
            st.subheader("Well Markers Mode")
            # Display HFC percentile value from Step 2
            hfc_val = st.session_state.get('hfc_p95')
            if hfc_val is not None:
                st.info(f"Using HFC percentile value from Step 2: {hfc_val:.3f}")
            else:
                st.warning("HFC percentile value not configured. Please complete Step 2.")
            if st.session_state.well_df is not None and not st.session_state.well_df.empty:
                # Use the correct function to get available markers
                available_markers = get_surfaces(st.session_state.well_df) # Corrected function call
                st.session_state.selected_well_markers = st.multiselect(
                    "Select Well Markers",
                    options=available_markers,
                    default=available_markers[:min(len(available_markers), 3)]  # Default to first 3 or fewer
                )
                # --- Add well-marker pair dropdown ---
                # Get all well-marker pairs
                well_marker_dict = get_well_marker_pairs(st.session_state.well_df)
                # Filter pairs by selected markers
                filtered_pairs = [pair for pair in well_marker_dict.keys()
                                  if pair.split(" - ")[1] in st.session_state.selected_well_markers]
                if filtered_pairs:
                    # Default to selecting the first pair if none are selected yet, or if the previous single selection is in the list
                    default_selection = []
                    if st.session_state.get('selected_well_marker_pairs'):
                        # If it was a single string before, make it a list
                        current_selection = st.session_state.selected_well_marker_pairs
                        if isinstance(current_selection, str):
                            current_selection = [current_selection]
                        # Keep valid previous selections that are in the filtered_pairs
                        default_selection = [s for s in current_selection if s in filtered_pairs]

                    if not default_selection and filtered_pairs: # If still empty and pairs exist, pick the first one
                        default_selection = [filtered_pairs[0]]

                    st.session_state.selected_well_marker_pairs = st.multiselect(
                        "Select Well-Marker Pair(s)", # Changed label
                        options=filtered_pairs,
                        default=default_selection, # Ensure default is a list
                        key="well_marker_pair_multiselect" # New key might be good
                    )
                else:
                    st.session_state.selected_well_marker_pairs = None
                    st.info("No well-marker pairs available for the selected markers.")
                st.session_state.plot_mode_wells = st.radio(
                    "Plot Mode",
                    options=[1, 2],
                    format_func=lambda x: "Individual Plots" if x == 1 else "Comparative Plots",
                    index=0
                )
                st.session_state.plot_twt = st.checkbox("Plot Two-Way Travel Time (TWT)", value=False)
            else:
                st.warning("No well data available. Please upload well data in Step 1.")

        # Mode-specific UI elements based on selection
        if st.session_state.selection_mode == "Single inline (all crosslines)":
            st.subheader("Single Inline Mode")
            # Get min and max inline numbers
            if st.session_state.header_loader:
                min_inline = int(np.min(st.session_state.header_loader.inlines))
                max_inline = int(np.max(st.session_state.header_loader.inlines))
                
                # Default to previously selected inline or min inline
                default_inline = st.session_state.selected_inline if st.session_state.selected_inline else min_inline
                
                # Inline number selection
                st.session_state.selected_inline = st.number_input(
                    f"Specify an inline number ({min_inline}-{max_inline}):",
                    min_value=min_inline,
                    max_value=max_inline,
                    value=default_inline,
                    step=1
                )
                
                # Batch size selection if GPU is available
                # When using this page for unified mode selection, make sure we re-use the GPU code
                try:
                    from common.ui_elements import get_suggested_batch_size
                    GPU_AVAILABLE = 'dlogst_spec_descriptor_gpu' in globals() or 'dlogst_spec_descriptor_gpu' in st.session_state
                    if GPU_AVAILABLE:
                        suggested_batch, free_mb = get_suggested_batch_size()
                        st.info(f"Estimated free GPU memory: {free_mb:.1f} MB. Suggested batch size: {suggested_batch}")
                        st.session_state.batch_size = st.number_input(
                            "Batch size for GPU processing:",
                            min_value=10,
                            max_value=4000,
                            value=suggested_batch,
                            step=10,
                            help="Number of traces to process at once. Higher values use more GPU memory."
                        )
                except Exception as gpu_err:
                    logging.warning(f"Error configuring GPU options: {gpu_err}")
                    st.warning("GPU processing options not available. Processing may be slower.")
            else:
                st.warning("SEG-Y headers not loaded yet.")
                
        elif st.session_state.selection_mode == "Single crossline (all inlines)":
            st.subheader("Single Crossline Mode")
            if st.session_state.header_loader:
                # Get min and max crossline numbers
                min_crossline = int(np.min(st.session_state.header_loader.crosslines))
                max_crossline = int(np.max(st.session_state.header_loader.crosslines))

                # Default to previously selected crossline or min crossline
                default_crossline = st.session_state.selected_crossline if st.session_state.selected_crossline else min_crossline

                # Crossline number selection
                st.session_state.selected_crossline = st.number_input(
                    f"Specify a crossline number ({min_crossline}-{max_crossline}):",
                    min_value=min_crossline,
                    max_value=max_crossline,
                    value=default_crossline,
                    step=1
                )

                # Batch size selection if GPU is available
                try:
                    from common.ui_elements import get_suggested_batch_size
                    GPU_AVAILABLE = 'dlogst_spec_descriptor_gpu' in globals() or 'dlogst_spec_descriptor_gpu' in st.session_state
                    if GPU_AVAILABLE:
                        suggested_batch, free_mb = get_suggested_batch_size()
                        st.info(f"Estimated free GPU memory: {free_mb:.1f} MB. Suggested batch size: {suggested_batch}")
                        st.session_state.batch_size = st.number_input(
                            "Batch size for GPU processing:",
                            min_value=10,
                            max_value=4000,
                            value=suggested_batch,
                            step=10,
                            help="Number of traces to process at once. Higher values use more GPU memory."
                        )
                except Exception as gpu_err:
                    logging.warning(f"Error configuring GPU options: {gpu_err}")
                    st.warning("GPU processing options not available. Processing may be slower.")
            else:
                st.warning("SEG-Y headers not loaded yet.")

        # By inline/crossline section (AOI) Mode
        elif st.session_state.selection_mode == "By inline/crossline section (AOI)":
            st.subheader("Single Crossline Mode")
            if st.session_state.header_loader:
                # Get min and max crossline numbers
                min_crossline = int(np.min(st.session_state.header_loader.crosslines))
                max_crossline = int(np.max(st.session_state.header_loader.crosslines))

                # Default to previously selected crossline or min crossline
                default_crossline = st.session_state.selected_crossline if st.session_state.selected_crossline else min_crossline

                # Crossline number selection
                st.session_state.selected_crossline = st.number_input(
                    f"Specify a crossline number ({min_crossline}-{max_crossline}):",
                    min_value=min_crossline,
                    max_value=max_crossline,
                    value=default_crossline,
                    step=1,
                    key="crossline_number_input"
                )
                
                # Batch size selection if GPU is available
                if st.session_state.get('GPU_AVAILABLE', False):
                    suggested_batch, free_mb = get_suggested_batch_size()
                    st.info(f"Estimated free GPU memory: {free_mb:.1f} MB. Suggested batch size: {suggested_batch}")
                    st.session_state.batch_size = st.number_input(
                        "Batch size for GPU processing:",
                        min_value=10,
                        max_value=4000,
                        value=suggested_batch,
                        step=10,
                        help="Number of traces to process at once. Higher values use more GPU memory.",
                        key="batch_size_input_crossline"
                    )
                else:
                    st.warning("GPU processing not available. Processing will be slower.")
                    st.session_state.batch_size = None
                
                # Button to load traces for the selected crossline
                if st.button("Load Traces", key="load_crossline_traces"):
                    with st.spinner(f"Loading traces for crossline {st.session_state.selected_crossline}..."):
                        try:
                            # Filter traces by the selected crossline
                            crossline_mask = st.session_state.header_loader.crosslines == st.session_state.selected_crossline
                            selected_indices = st.session_state.header_loader.unique_indices[crossline_mask]
                            
                            if len(selected_indices) == 0:
                                st.error(f"No traces found for crossline {st.session_state.selected_crossline}.")
                            else:
                                # Load the trace data
                                loaded_trace_data = []
                                for idx in selected_indices:
                                    trace_sample = load_trace_sample(
                                        st.session_state.header_loader.source_file_path,
                                        idx
                                    )
                                    loaded_trace_data.append({
                                        'trace_sample': trace_sample,
                                        'trace_idx': idx,
                                        'inline': st.session_state.header_loader.inlines[idx],
                                        'crossline': st.session_state.selected_crossline
                                    })
                                
                                # Store in session state
                                st.session_state.loaded_trace_data = loaded_trace_data
                                st.session_state.selected_indices = selected_indices.tolist()
                                st.session_state.area_selected = True
                                st.success(f"Successfully loaded {len(loaded_trace_data)} traces for crossline {st.session_state.selected_crossline}.")
                                
                                # Proceed to analyze data
                                st.session_state.current_step = "analyze_data"
                                st.rerun()
                                
                        except Exception as e:
                            st.error(f"Error loading traces: {e}")
                            logging.error(f"Error loading traces for crossline: {e}", exc_info=True)
            else:
                st.warning("SEG-Y headers not loaded yet.")

        # AOI Mode
        elif st.session_state.selection_mode == "By inline/crossline section (AOI)":
            st.subheader("AOI Mode")

            # Get actual inline/crossline ranges from the loaded SEG-Y file
            if st.session_state.header_loader:
                # Extract actual inline/crossline ranges from the header loader
                inlines = st.session_state.header_loader.inlines
                crosslines = st.session_state.header_loader.crosslines

                # Get the full inline/crossline range using the new method
                range_dict = st.session_state.header_loader.get_inline_crossline_range()
                actual_inline_min = range_dict['inline_min']
                actual_inline_max = range_dict['inline_max']
                actual_xline_min = range_dict['xline_min']
                actual_xline_max = range_dict['xline_max']

                # Initialize session state values if not already set
                if st.session_state.aoi_inline_min is None:
                    st.session_state.aoi_inline_min = actual_inline_min
                if st.session_state.aoi_inline_max is None:
                    st.session_state.aoi_inline_max = actual_inline_max
                if st.session_state.aoi_xline_min is None:
                    st.session_state.aoi_xline_min = actual_xline_min
                if st.session_state.aoi_xline_max is None:
                    st.session_state.aoi_xline_max = actual_xline_max

                # No need to initialize fixed values for specific inline/crossline as we only support Full AOI
                st.session_state.aoi_plot_fixed_value = None

            # Display AOI bounds
            col1, col2 = st.columns(2)
            with col1:
                st.session_state.aoi_inline_min = st.number_input(
                    "Inline Min",
                    value=st.session_state.aoi_inline_min or 0,
                    step=1,
                    key="aoi_inline_min_input"
                )
                st.session_state.aoi_xline_min = st.number_input(
                    "Crossline Min",
                    value=st.session_state.aoi_xline_min or 0,
                    step=1,
                    key="aoi_xline_min_input"
                )

            with col2:
                st.session_state.aoi_inline_max = st.number_input(
                    "Inline Max",
                    value=st.session_state.aoi_inline_max or 100,
                    step=1,
                    key="aoi_inline_max_input"
                )
                st.session_state.aoi_xline_max = st.number_input(
                    "Crossline Max",
                    value=st.session_state.aoi_xline_max or 100,
                    step=1,
                    key="aoi_xline_max_input"
                )

            # Processing option selection - only Full AOI is available
            st.session_state.aoi_processing_option = "Full AOI"  # Set directly to Full AOI
            st.info("Processing Option: Full AOI")

            # No specific inline/crossline input needed as we only support Full AOI
            st.session_state.aoi_plot_section_type = None
            st.session_state.aoi_plot_fixed_value = None

            # Also ensure the headers dataframe is properly loaded and filtered
            if st.session_state.header_loader:
                headers_df = pd.DataFrame({
                    'inline': st.session_state.header_loader.inlines,
                    'crossline': st.session_state.header_loader.crosslines,
                    'x': st.session_state.header_loader.x_coords,
                    'y': st.session_state.header_loader.y_coords
                })
                headers_df['trace_idx'] = st.session_state.header_loader.unique_indices
            else:
                st.warning("SEG-Y headers not loaded yet.")
                headers_df = pd.DataFrame()

            # Store the filtered AOI bounds for reference
            st.session_state.aoi_bounds = {
                'inline_min': st.session_state.aoi_inline_min,
                'inline_max': st.session_state.aoi_inline_max,
                'xline_min': st.session_state.aoi_xline_min,
                'xline_max': st.session_state.aoi_xline_max
            }

        # Polyline Mode
        elif st.session_state.selection_mode == "By Polyline File Import":
            st.subheader("Polyline Mode")

            # Polyline File Upload
            st.subheader("Upload Polyline File")
            polyline_file = st.file_uploader("Choose a polyline file", type=["txt", "csv"], key="polyline_file_uploader")
            if polyline_file is not None:
                st.session_state.polyline_file_info = {
                    'name': polyline_file.name,
                    'buffer': polyline_file
                }
                st.success(f"Uploaded {polyline_file.name}")

                # Show tolerance slider only after file is uploaded
                st.session_state.polyline_tolerance = st.slider(
                    "Polyline Tolerance",
                    min_value=0.0,
                    max_value=20.0,  # Maximum value of 20.0
                    value=st.session_state.polyline_tolerance,
                    step=1.0,  # Changed from 0.1 to 1.0
                    key="polyline_tolerance_slider",
                    help="Maximum distance from polyline to include traces (larger values select more traces)"
                )
            else:
                st.warning("Please upload a polyline file before proceeding.")

        # Add a separator before the proceed button
        st.markdown("---")

        # Proceed Button with a more specific key and primary styling
        st.markdown("### Complete Step 3 and Continue")

        # Check if display_params_configured is set, if not, set it to True if stats_defaults is available
        if not st.session_state.get('display_params_configured') and st.session_state.get('stats_defaults') is not None:
            logging.info("Setting display_params_configured to True as stats_defaults is available")
            st.session_state.display_params_configured = True

        # Log current state for debugging
        logging.info(f"Before button click - display_params_configured: {st.session_state.get('display_params_configured')}")
        logging.info(f"Before button click - area_selected: {st.session_state.get('area_selected')}")

        if st.button("Next: Analyze Data", key="select_area_next_button", use_container_width=True, type="primary"):
            # Process selection based on mode
            if st.session_state.selection_mode == "By well markers":
                selected_labels = st.session_state.get('selected_well_marker_pairs', []) # Get the labels from the multiselect
                if not selected_labels:
                    st.warning("Please select at least one well-marker pair to continue.")
                    st.stop() # Stop execution to prevent transition

                selected_pairs_data = []
                all_selected_trace_indices = []
                well_df = st.session_state.get('well_df') # Get well_df safely

                if well_df is not None and not well_df.empty:
                    with st.spinner("Processing selected well-marker pairs..."):
                        for label in selected_labels:
                            try:
                                well_name, surface_name = label.split(" - ", 1)
                                # Find the row(s) in well_df matching this pair
                                pair_df = well_df[(well_df['Well'] == well_name) & (well_df['Surface'] == surface_name)]

                                if not pair_df.empty:
                                    # Assuming one row per unique well-marker pair label in well_df
                                    pair_data = pair_df.iloc[0].to_dict()
                                    selected_pairs_data.append(pair_data)

                                    # Find the nearest trace index for this pair's coordinates
                                    if st.session_state.header_loader:
                                        trace_idx = get_nearest_trace_index(st.session_state.header_loader, pair_data['X'], pair_data['Y'])
                                        if trace_idx is not None:
                                            all_selected_trace_indices.append(trace_idx)
                                        else:
                                            logging.warning(f"Could not find nearest trace for well-marker pair: {label}")
                                    else:
                                         st.error("SEG-Y headers not loaded. Cannot find trace indices.")
                                         st.stop() # Stop execution

                            except Exception as e:
                                logging.error(f"Error processing well-marker pair {label}: {e}")
                                st.error(f"Error processing well-marker pair {label}. See logs for details.")
                                st.stop() # Stop execution

                    # Store the structured data and aggregated indices
                    st.session_state.selected_well_marker_pairs = selected_pairs_data # Overwrite labels with structured data
                    st.session_state.selected_indices = list(set(all_selected_trace_indices)) # Store unique indices
                    st.session_state.area_selected_details = {'type': 'well_markers', 'count': len(selected_pairs_data), 'labels': selected_labels}
                    st.session_state.area_selected = True
                    st.session_state.area_selected_mode = 'well_markers'
                    st.session_state.selected_data_for_precompute = selected_pairs_data # Set data for precompute

                    # Set precomputation_complete to True to skip that step
                    st.session_state.precomputation_complete = True
                    logging.info("Setting precomputation_complete flag to True to skip precomputation step")

                    # Load trace data for selected well markers
                    loaded_trace_data = []
                    for idx, pair_data in enumerate(selected_pairs_data):
                        trace_idx = st.session_state.selected_indices[idx] if idx < len(st.session_state.selected_indices) else None
                        if trace_idx is not None:
                            # Load the trace sample
                            trace_sample = load_trace_sample(st.session_state.header_loader.source_file_path, trace_idx)

                            # Create trace data dictionary
                            trace_data = {
                                'trace_sample': trace_sample,
                                'trace_idx': trace_idx,
                                'well_marker_name': f"{pair_data.get('Well', 'Unknown')} - {pair_data.get('Surface', 'Unknown')}",
                                'marker_value': pair_data.get('TWT_sec', None) if st.session_state.plot_twt else None
                            }
                            loaded_trace_data.append(trace_data)

                    # Store loaded trace data in session state
                    st.session_state.loaded_trace_data = loaded_trace_data

                    st.success(f"{len(selected_pairs_data)} well-marker pair(s) selected. Found {len(st.session_state.selected_indices)} traces. Proceeding to Analyze Data.")
                    st.session_state.current_step = "analyze_data" # Transition directly to analyze_data
                    st.rerun()

                else:
                    st.warning("Well data not loaded or empty. Cannot process well-marker selections.")
                    st.stop() # Stop execution

            elif st.session_state.selection_mode == "Single inline (all crosslines)":
                # Logic for Single Inline - need to get all trace indices for the selected inline
                if st.session_state.header_loader and st.session_state.get('selected_inline') is not None:
                    headers_df = pd.DataFrame({
                        'inline': st.session_state.header_loader.inlines,
                        'crossline': st.session_state.header_loader.crosslines,
                        'trace_idx': st.session_state.header_loader.unique_indices
                    })
                    selected_inline_df = headers_df[headers_df['inline'] == st.session_state.selected_inline]
                    st.session_state.selected_indices = selected_inline_df['trace_idx'].tolist()
                    st.session_state.area_selected_details = {'type': 'single_inline', 'inline': st.session_state.selected_inline, 'count': len(st.session_state.selected_indices)}
                    st.session_state.area_selected = True
                    st.session_state.area_selected_mode = 'single_inline'
                    st.session_state.selected_data_for_precompute = None # Or relevant data for this mode
                    # Set precomputation_complete to True to skip that step
                    st.session_state.precomputation_complete = True

                    st.success(f"Selected inline {st.session_state.selected_inline}. Found {len(st.session_state.selected_indices)} traces. Proceeding to Analyze Data.")
                    st.session_state.current_step = "analyze_data"
                    st.rerun()
                else:
                    st.warning("Please select an inline number.")
                    st.stop()

            elif st.session_state.selection_mode == "Single crossline (all inlines)":
                # Logic for Single Crossline - need to get all trace indices for the selected crossline
                if st.session_state.header_loader and st.session_state.get('selected_crossline') is not None:
                    headers_df = pd.DataFrame({
                        'inline': st.session_state.header_loader.inlines,
                        'crossline': st.session_state.header_loader.crosslines,
                        'trace_idx': st.session_state.header_loader.unique_indices
                    })
                    selected_crossline_df = headers_df[headers_df['crossline'] == st.session_state.selected_crossline]
                    st.session_state.selected_indices = selected_crossline_df['trace_idx'].tolist()
                    st.session_state.area_selected_details = {'type': 'single_crossline', 'crossline': st.session_state.selected_crossline, 'count': len(st.session_state.selected_indices)}
                    st.session_state.area_selected = True
                    st.session_state.area_selected_mode = 'single_crossline'
                    st.session_state.selected_data_for_precompute = None # Or relevant data for this mode
                    # Set precomputation_complete to True to skip that step
                    st.session_state.precomputation_complete = True

                    st.success(f"Selected crossline {st.session_state.selected_crossline}. Found {len(st.session_state.selected_indices)} traces. Proceeding to Analyze Data.")
                    st.session_state.current_step = "analyze_data"
                    st.rerun()
                else:
                    st.warning("Please select a crossline number.")
                    st.stop()

            elif st.session_state.selection_mode == "By inline/crossline section (AOI)":
                # Logic for AOI - need to get all trace indices within the selected AOI bounds
                if st.session_state.header_loader and st.session_state.get('aoi_bounds') is not None:
                     headers_df = pd.DataFrame({
                        'inline': st.session_state.header_loader.inlines,
                        'crossline': st.session_state.header_loader.crosslines,
                        'trace_idx': st.session_state.header_loader.unique_indices
                    })
                     aoi_bounds = st.session_state.aoi_bounds
                     aoi_df = headers_df[
                        (headers_df['inline'] >= aoi_bounds['inline_min']) &
                        (headers_df['inline'] <= aoi_bounds['inline_max']) &
                        (headers_df['crossline'] >= aoi_bounds['xline_min']) &
                        (headers_df['crossline'] <= aoi_bounds['xline_max'])
                    ]
                     st.session_state.selected_indices = aoi_df['trace_idx'].tolist()
                     st.session_state.area_selected_details = {'type': 'aoi', 'bounds': aoi_bounds, 'count': len(st.session_state.selected_indices)}
                     st.session_state.area_selected = True
                     st.session_state.area_selected_mode = 'aoi'
                     st.session_state.selected_data_for_precompute = None # Or relevant data for this mode
                     # Set precomputation_complete to True to skip that step
                     st.session_state.precomputation_complete = True

                     st.success(f"Selected AOI. Found {len(st.session_state.selected_indices)} traces. Proceeding to Analyze Data.")
                     st.session_state.current_step = "analyze_data"
                     st.rerun()
                else:
                    st.warning("Please define the AOI bounds.")
                    st.stop()

            elif st.session_state.selection_mode == "By Polyline File Import":
                # Logic for Polyline - need to get trace indices near the polyline
                if st.session_state.header_loader and st.session_state.get('polyline_file_info') is not None and st.session_state.get('polyline_tolerance') is not None:
                    # Assuming polyline data is loaded and parsed elsewhere and stored in session state
                    # For now, we'll just check if the file info exists. The actual trace finding logic
                    # would need to be implemented or called here.
                    # Placeholder:
                    st.warning("Polyline trace selection logic not fully implemented yet.")
                    # Assuming selected_indices would be populated by a function call here
                    # st.session_state.selected_indices = find_traces_near_polyline(...)
                    # st.session_state.area_selected_details = {'type': 'polyline', 'file': st.session_state.polyline_file_info['name'], 'tolerance': st.session_state.polyline_tolerance, 'count': len(st.session_state.selected_indices)}
                    # st.session_state.area_selected = True
                    # st.session_state.area_selected_mode = 'polyline'
                    # st.session_state.selected_data_for_precompute = None # Or relevant data for this mode
                    # st.session_state.current_step = "precompute_qc"
                    # st.rerun()
                    st.stop() # Prevent transition until polyline logic is ready
                else:
                    st.warning("Please upload a polyline file and set tolerance.")
                    st.stop()

            else:
                st.warning("Unknown selection mode. Please select a valid mode.")
                st.stop()

        # Add a "Start New Analysis" button to the sidebar
        st.sidebar.markdown("---")
        if st.sidebar.button("🔄 Start New Analysis", use_container_width=True):
            reset_state()
            st.success("Starting new analysis. All temporary data has been cleared.")
            st.rerun()
        return

    # --- Trace selection logic (formerly in Proceed button) ---
    st.header("Step 3.5: Select Traces for Analysis")
    # Well Markers Mode
    if st.session_state.selection_mode == "By well markers":
        st.subheader("Well Markers Mode")
        if st.session_state.well_df is not None and not st.session_state.well_df.empty:
            # Use the correct function to get available markers
            available_markers = get_surfaces(st.session_state.well_df) # Corrected function call
            st.session_state.selected_well_markers = st.multiselect(
                "Select Well Markers",
                options=available_markers,
                default=available_markers[:min(len(available_markers), 3)]  # Default to first 3 or fewer
            )
            # --- Add well-marker pair dropdown ---
            # Get all well-marker pairs
            well_marker_dict = get_well_marker_pairs(st.session_state.well_df)
            # Filter pairs by selected markers
            filtered_pairs = [pair for pair in well_marker_dict.keys()
                              if pair.split(" - ")[1] in st.session_state.selected_well_markers]
            if filtered_pairs:
                # Default to selecting the first pair if none are selected yet, or if the previous single selection is in the list
                default_selection = []
                if st.session_state.get('selected_well_marker_pairs'):
                    # If it was a single string before, make it a list
                    current_selection = st.session_state.selected_well_marker_pairs
                    if isinstance(current_selection, str):
                        current_selection = [current_selection]
                    # Keep valid previous selections that are in the filtered_pairs
                    default_selection = [s for s in current_selection if s in filtered_pairs]

                if not default_selection and filtered_pairs: # If still empty and pairs exist, pick the first one
                    default_selection = [filtered_pairs[0]]

                st.session_state.selected_well_marker_pairs = st.multiselect(
                    "Select Well-Marker Pair(s)", # Changed label
                    options=filtered_pairs,
                    default=default_selection, # Ensure default is a list
                    key="well_marker_pair_multiselect_trace_select" # New key for trace selection block
                )
            else:
                st.session_state.selected_well_marker_pairs = None
                st.info("No well-marker pairs available for the selected markers.")
            st.session_state.plot_mode_wells = st.radio(
                "Plot Mode",
                options=[1, 2],
                format_func=lambda x: "Individual Plots" if x == 1 else "Comparative Plots",
                index=0
            )
            st.session_state.plot_twt = st.checkbox("Plot Two-Way Travel Time (TWT)", value=False)
        else:
            st.warning("No well data available. Please upload well data in Step 1.")

    # Single Inline Mode
    elif st.session_state.selection_mode == "Single inline (all crosslines)":
        st.subheader("Single Inline Mode")
        if st.session_state.header_loader:
            # Get min and max inline numbers
            min_inline = int(np.min(st.session_state.header_loader.inlines))
            max_inline = int(np.max(st.session_state.header_loader.inlines))

            # Default to previously selected inline or min inline
            default_inline = st.session_state.selected_inline if st.session_state.selected_inline else min_inline

            # Inline number selection
            st.session_state.selected_inline = st.number_input(
                f"Specify an inline number ({min_inline}-{max_inline}):",
                min_value=min_inline,
                max_value=max_inline,
                value=default_inline,
                step=1
            )

            # Batch size selection if GPU is available
            if st.session_state.get('GPU_AVAILABLE', False):
                suggested_batch, free_mb = get_suggested_batch_size()
                st.info(f"Estimated free GPU memory: {free_mb:.1f} MB. Suggested batch size: {suggested_batch}")
                st.session_state.batch_size = st.number_input(
                    "Batch size for GPU processing:",
                    min_value=10,
                    max_value=4000,
                    value=suggested_batch,
                    step=10,
                    help="Number of traces to process at once. Higher values use more GPU memory."
                )
            else:
                st.warning("GPU processing not available. Processing will be slower.")
                st.session_state.batch_size = None

            # Add a "Next: Analyze Data" button specifically for Single Inline mode
            st.markdown("---")
            if st.button("Next: Analyze Data", key="single_inline_next_button", use_container_width=True, type="primary"):
                # Logic for Single Inline - need to get all trace indices for the selected inline
                if st.session_state.header_loader and st.session_state.get('selected_inline') is not None:
                    headers_df = pd.DataFrame({
                        'inline': st.session_state.header_loader.inlines,
                        'crossline': st.session_state.header_loader.crosslines,
                        'trace_idx': st.session_state.header_loader.unique_indices
                    })
                    selected_inline_df = headers_df[headers_df['inline'] == st.session_state.selected_inline]
                    st.session_state.selected_indices = selected_inline_df['trace_idx'].tolist()
                    st.session_state.area_selected_details = {'type': 'single_inline', 'inline': st.session_state.selected_inline, 'count': len(st.session_state.selected_indices)}
                    st.session_state.area_selected = True
                    st.session_state.area_selected_mode = 'single_inline'
                    st.session_state.selected_data_for_precompute = None # Or relevant data for this mode
                    # Set precomputation_complete to True to skip that step
                    st.session_state.precomputation_complete = True

                    st.success(f"Selected inline {st.session_state.selected_inline}. Found {len(st.session_state.selected_indices)} traces. Proceeding to Analyze Data.")
                    st.session_state.current_step = "analyze_data"
                    st.rerun()
                else:
                    st.warning("Please select an inline number.")
                    st.stop()
        else:
            st.warning("SEG-Y headers not loaded yet.")

    # Single Crossline Mode
    elif st.session_state.selection_mode == "Single crossline (all inlines)":
        st.subheader("Single Crossline Mode")
        if st.session_state.header_loader:
            # Get min and max crossline numbers
            min_crossline = int(np.min(st.session_state.header_loader.crosslines))
            max_crossline = int(np.max(st.session_state.header_loader.crosslines))

            # Default to previously selected crossline or min crossline
            default_crossline = st.session_state.selected_crossline if st.session_state.selected_crossline else min_crossline

            # Crossline number selection
            st.session_state.selected_crossline = st.number_input(
                f"Specify a crossline number ({min_crossline}-{max_crossline}):",
                min_value=min_crossline,
                max_value=max_crossline,
                value=default_crossline,
                step=1
            )

            # Batch size selection if GPU is available
            if st.session_state.get('GPU_AVAILABLE', False):
                suggested_batch, free_mb = get_suggested_batch_size()
                st.info(f"Estimated free GPU memory: {free_mb:.1f} MB. Suggested batch size: {suggested_batch}")
                st.session_state.batch_size = st.number_input(
                    "Batch size for GPU processing:",
                    min_value=10,
                    max_value=4000,
                    value=suggested_batch,
                    step=10,
                    help="Number of traces to process at once. Higher values use more GPU memory."
                )
            else:
                st.warning("GPU processing not available. Processing will be slower.")
                st.session_state.batch_size = None
        else:
            st.warning("SEG-Y headers not loaded yet.")

    # AOI Mode
    elif st.session_state.selection_mode == "By inline/crossline section (AOI)":
        st.subheader("AOI Mode")

        # Get actual inline/crossline ranges from the loaded SEG-Y file
        if st.session_state.header_loader:
            # Extract actual inline/crossline ranges from the header loader
            inlines = st.session_state.header_loader.inlines
            crosslines = st.session_state.header_loader.crosslines

            # Get the full inline/crossline range using the new method
            range_dict = st.session_state.header_loader.get_inline_crossline_range()
            actual_inline_min = range_dict['inline_min']
            actual_inline_max = range_dict['inline_max']
            actual_xline_min = range_dict['xline_min']
            actual_xline_max = range_dict['xline_max']

            # Initialize session state values if not already set
            if st.session_state.aoi_inline_min is None:
                st.session_state.aoi_inline_min = actual_inline_min
            if st.session_state.aoi_inline_max is None:
                st.session_state.aoi_inline_max = actual_inline_max
            if st.session_state.aoi_xline_min is None:
                st.session_state.aoi_xline_min = actual_xline_min
            if st.session_state.aoi_xline_max is None:
                st.session_state.aoi_xline_max = actual_xline_max

            # No need to initialize fixed values for specific inline/crossline as we only support Full AOI
            st.session_state.aoi_plot_fixed_value = None

        # Display AOI bounds
        col1, col2 = st.columns(2)
        with col1:
            st.session_state.aoi_inline_min = st.number_input(
                "Inline Min",
                value=st.session_state.aoi_inline_min or 0,
                step=1,
                key="aoi_inline_min_input"
            )
            st.session_state.aoi_xline_min = st.number_input(
                "Crossline Min",
                value=st.session_state.aoi_xline_min or 0,
                step=1,
                key="aoi_xline_min_input"
            )

        with col2:
            st.session_state.aoi_inline_max = st.number_input(
                "Inline Max",
                value=st.session_state.aoi_inline_max or 100,
                step=1,
                key="aoi_inline_max_input"
            )
            st.session_state.aoi_xline_max = st.number_input(
                "Crossline Max",
                value=st.session_state.aoi_xline_max or 100,
                step=1,
                key="aoi_xline_max_input"
            )

        # Processing option selection - only Full AOI is available
        st.session_state.aoi_processing_option = "Full AOI"  # Set directly to Full AOI
        st.info("Processing Option: Full AOI")

        # No specific inline/crossline input needed as we only support Full AOI
        st.session_state.aoi_plot_section_type = None
        st.session_state.aoi_plot_fixed_value = None

        # Also ensure the headers dataframe is properly loaded and filtered
        if st.session_state.header_loader:
            headers_df = pd.DataFrame({
                'inline': st.session_state.header_loader.inlines,
                'crossline': st.session_state.header_loader.crosslines,
                'x': st.session_state.header_loader.x_coords,
                'y': st.session_state.header_loader.y_coords
            })
            headers_df['trace_idx'] = st.session_state.header_loader.unique_indices
        else:
            st.warning("SEG-Y headers not loaded yet.")
            headers_df = pd.DataFrame()

        # Store the filtered AOI bounds for reference
        st.session_state.aoi_bounds = {
            'inline_min': st.session_state.aoi_inline_min,
            'inline_max': st.session_state.aoi_inline_max,
            'xline_min': st.session_state.aoi_xline_min,
            'xline_max': st.session_state.aoi_xline_max
        }

    # Polyline Mode
    elif st.session_state.selection_mode == "By Polyline File Import":
        st.subheader("Polyline Mode")

        # Polyline File Upload
        st.subheader("Upload Polyline File")
        polyline_file = st.file_uploader("Choose a polyline file", type=["txt", "csv"], key="polyline_file_uploader")
        if polyline_file is not None:
            st.session_state.polyline_file_info = {
                'name': polyline_file.name,
                'buffer': polyline_file
            }
            st.success(f"Uploaded {polyline_file.name}")

            # Show tolerance slider only after file is uploaded
            st.session_state.polyline_tolerance = st.slider(
                "Polyline Tolerance",
                min_value=0.0,
                max_value=20.0,  # Maximum value of 20.0
                value=st.session_state.polyline_tolerance,
                step=1.0,  # Changed from 0.1 to 1.0
                key="polyline_tolerance_slider",
                help="Maximum distance from polyline to include traces (larger values select more traces)"
            )
        else:
            st.warning("Please upload a polyline file before proceeding.")

    # Add a separator before the proceed button
    st.markdown("---")

    # Proceed Button with a more specific key and primary styling
    st.markdown("### Complete Step 3 and Continue")

    # Check if display_params_configured is set, if not, set it to True if stats_defaults is available
    if not st.session_state.get('display_params_configured') and st.session_state.get('stats_defaults') is not None:
        logging.info("Setting display_params_configured to True as stats_defaults is available")
        st.session_state.display_params_configured = True

    # Log current state for debugging
    logging.info(f"Before button click - display_params_configured: {st.session_state.get('display_params_configured')}")
    logging.info(f"Before button click - area_selected: {st.session_state.get('area_selected')}")

    if st.button("Proceed to Pre-computation & QC (Step 3.5) ➡️", key="select_area_proceed_button", use_container_width=True, type="primary"):
        logging.info("Proceed button clicked in Select Area page")
        with st.spinner("Preparing selected data..."):
            # Determine selected indices based on selection mode
            if st.session_state.selection_mode == "By well markers":
                if st.session_state.get('selected_well_marker_pairs') and isinstance(st.session_state.selected_well_marker_pairs, list):
                    temp_selected_indices = []
                    temp_selected_data_for_precompute = []
                    well_df = st.session_state.well_df

                    for pair_label in st.session_state.selected_well_marker_pairs:
                        well_name, surface_name = pair_label.split(" - ")
                        # Find the original data for this pair
                        marker_data_row = well_df[
                            (well_df['Well'] == well_name) & (well_df['Surface'] == surface_name)
                        ]
                        if not marker_data_row.empty:
                            marker_info = marker_data_row.iloc[0].to_dict()
                            trace_idx = get_nearest_trace_index(
                                st.session_state.header_loader,
                                marker_info["X"],
                                marker_info["Y"]
                            )
                            if trace_idx is not None:
                                temp_selected_indices.append(trace_idx)
                                # Store relevant info for precomputation, e.g., the marker_info dict
                                # and the trace_idx itself.
                                temp_selected_data_for_precompute.append({
                                    "well_name": well_name,
                                    "surface_name": surface_name,
                                    "x_coord": marker_info["X"],
                                    "y_coord": marker_info["Y"],
                                    "z_coord": marker_info.get("Z"), # Or TWT_sec if used
                                    "trace_index": trace_idx,
                                    "original_marker_data": marker_info
                                })

                    # Remove duplicate trace indices if necessary, though for well markers,
                    # different pairs usually mean different locations.
                    st.session_state.selected_indices = list(set(temp_selected_indices))
                    st.session_state.selected_data_for_precompute = temp_selected_data_for_precompute

                    # Always set area_selected to True if we have selected well-marker pairs,
                    # even if no valid traces were found (we'll handle this in precompute_qc)
                    st.session_state.area_selected = True

                    st.session_state.area_selected_details = {
                        'type': 'well_markers',
                        'count': len(st.session_state.selected_indices),
                        'pairs_count': len(st.session_state.selected_well_marker_pairs)
                    }

                    # Set flag to automatically run precomputation when transitioning to Step 3.5
                    st.session_state.auto_run_precomputation = True
                    logging.info("Setting auto_run_precomputation flag to True for well-marker selection (trace selection)")

                    if st.session_state.selected_indices:
                        st.success(f"{len(st.session_state.selected_well_marker_pairs)} well-marker pair(s) selected, resulting in {len(st.session_state.selected_indices)} unique traces.")
                    else:
                        # Still proceed, but show a warning
                        st.warning("No valid traces found for the selected well-marker pairs. You can still proceed to the next step, but you may need to adjust your selection.")
                else:
                    st.warning("No well-marker pairs currently selected.")
                    # Still set area_selected to True to allow proceeding to the next step
                    st.session_state.area_selected = True
                    st.session_state.selected_data_for_precompute = []
                    st.session_state.selected_indices = []

            elif st.session_state.selection_mode == "Single inline (all crosslines)":
                # Single inline mode - find all traces with the selected inline
                selected_indices = []
                if st.session_state.header_loader:
                    # Get all traces with the selected inline
                    inlines = st.session_state.header_loader.inlines
                    unique_indices = st.session_state.header_loader.unique_indices

                    # Find indices where inline matches selected inline
                    for i, inline in enumerate(inlines):
                        if inline == st.session_state.selected_inline:
                            selected_indices.append(unique_indices[i])

                st.session_state.selected_indices = selected_indices

                # Store selected data for pre-computation
                st.session_state.selected_data_for_precompute = {
                    "mode": "single_inline",
                    "selected_inline": st.session_state.selected_inline,
                    "batch_size": st.session_state.batch_size,
                    "selected_indices": selected_indices
                }

            elif st.session_state.selection_mode == "Single crossline (all inlines)":
                # Single crossline mode - find all traces with the selected crossline
                selected_indices = []
                if st.session_state.header_loader:
                    # Get all traces with the selected crossline
                    crosslines = st.session_state.header_loader.crosslines
                    unique_indices = st.session_state.header_loader.unique_indices

                    # Find indices where crossline matches selected crossline
                    for i, crossline in enumerate(crosslines):
                        if crossline == st.session_state.selected_crossline:
                            selected_indices.append(unique_indices[i])

                st.session_state.selected_indices = selected_indices

                # Store selected data for pre-computation
                st.session_state.selected_data_for_precompute = {
                    "mode": "single_crossline",
                    "selected_crossline": st.session_state.selected_crossline,
                    "batch_size": st.session_state.batch_size,
                    "selected_indices": selected_indices
                }

            elif st.session_state.selection_mode == "By inline/crossline section (AOI)":
                # AOI mode - find all traces within the AOI bounds
                selected_indices = []
                if st.session_state.header_loader:
                    # Get header dataframe
                    headers_df = pd.DataFrame({
                        'inline': st.session_state.header_loader.inlines,
                        'crossline': st.session_state.header_loader.crosslines,
                        'x': st.session_state.header_loader.x_coords,
                        'y': st.session_state.header_loader.y_coords
                    })
                    headers_df['trace_idx'] = st.session_state.header_loader.unique_indices

                    # Filter by inline/crossline range
                    aoi_df = headers_df[
                        (headers_df['inline'] >= st.session_state.aoi_inline_min) &
                        (headers_df['inline'] <= st.session_state.aoi_inline_max) &
                        (headers_df['crossline'] >= st.session_state.aoi_xline_min) &
                        (headers_df['crossline'] <= st.session_state.aoi_xline_max)
                    ]

                    selected_indices = aoi_df['trace_idx'].tolist()

                st.session_state.selected_indices = selected_indices

                # Store selected data for pre-computation
                st.session_state.selected_data_for_precompute = {
                    "mode": "aoi",
                    "aoi_inline_min": st.session_state.aoi_inline_min,
                    "aoi_inline_max": st.session_state.aoi_inline_max,
                    "aoi_xline_min": st.session_state.aoi_xline_min,
                    "aoi_xline_max": st.session_state.aoi_xline_max,
                    "aoi_processing_option": st.session_state.aoi_processing_option,
                    "selected_indices": selected_indices
                }

            elif st.session_state.selection_mode == "By Polyline File Import":
                # Polyline mode - find traces near the polyline
                selected_indices = []
                if st.session_state.polyline_file_info and st.session_state.header_loader:
                    try:
                        # Parse polyline file
                        polyline_file = st.session_state.polyline_file_info['buffer']
                        polyline_vertices = parse_polyline_string(polyline_file.getvalue().decode('utf-8'))

                        if polyline_vertices:
                            # Find traces near polyline
                            x_coords = st.session_state.header_loader.x_coords
                            y_coords = st.session_state.header_loader.y_coords
                            unique_indices = st.session_state.header_loader.unique_indices

                            selected_indices = find_traces_near_polyline(
                                polyline_vertices,
                                x_coords,
                                y_coords,
                                unique_indices,
                                tolerance=st.session_state.polyline_tolerance
                            )
                    except Exception as e:
                        st.error(f"Error processing polyline: {e}")
                        logging.error(f"Polyline processing failed: {e}", exc_info=True)

                st.session_state.selected_indices = selected_indices

                # Store selected data for pre-computation
                st.session_state.selected_data_for_precompute = {
                    "mode": "polyline",
                    "polyline_file_info": st.session_state.polyline_file_info,
                    "polyline_tolerance": st.session_state.polyline_tolerance,
                    "selected_indices": selected_indices
                }

            # Log the area_selected status
            logging.info(f"Area selected flag is {st.session_state.area_selected}. Selected {len(st.session_state.selected_indices)} traces.")

            # Ensure display_params_configured is set to True if stats_defaults is available
            if not st.session_state.get('display_params_configured') and st.session_state.get('stats_defaults') is not None:
                logging.info("Setting display_params_configured to True as stats_defaults is available")
                st.session_state.display_params_configured = True

            # Show success message with number of selected traces
            st.success(f"Selected {len(st.session_state.selected_indices)} traces for analysis.")

            # Explicitly ensure we have the necessary session state variables set
            if 'selected_indices' not in st.session_state or not st.session_state.selected_indices:
                logging.warning("No traces selected. Cannot proceed to next step.")
                st.error("No traces were selected. Please check your selection criteria and try again.")
                return

            # Log all important session state variables before transitioning
            logging.info(f"Before transition - display_params_configured: {st.session_state.get('display_params_configured')}")
            logging.info(f"Before transition - area_selected: {st.session_state.get('area_selected')}")
            logging.info(f"Before transition - selected_data_for_precompute: {st.session_state.get('selected_data_for_precompute') is not None}")
            logging.info(f"Before transition - selected_indices count: {len(st.session_state.get('selected_indices', []))}")

            # Navigate to the pre-computation & QC step
            logging.info("Transitioning to precompute_qc step")
            st.session_state.current_step = "precompute_qc"
            st.rerun()

    # Add a "Start New Analysis" button to the sidebar
    st.sidebar.markdown("---")
    if st.sidebar.button("🔄 Start New Analysis", use_container_width=True):
        reset_state()
        st.success("Starting new analysis. All temporary data has been cleared.")
        st.rerun()
