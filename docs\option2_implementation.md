# Option 2: Single Inline/Crossline Analysis Implementation

This document provides a detailed overview of the implementation for Option 2 (Single Inline/Crossline Analysis) in the WOSS Seismic Analysis Tool.

## Overview

Option 2 allows users to analyze a single inline or crossline section from a 3D seismic volume. The implementation includes:

1. Selection of a specific inline or crossline number
2. Loading of trace data for the selected section
3. Calculation of spectral descriptors for the entire section
4. Visualization of the results

## Key Components

### 1. User Interface

The user interface for Option 2 is implemented in two main files:

1. **select_area_page.py**: Handles the selection of inline/crossline and loading of trace data
2. **analyze_data_page.py**: Handles the calculation and visualization of spectral descriptors

### 2. Data Flow

1. **Selection Phase** (select_area_page.py):
   - User selects either "Single inline (all crosslines)" or "Single crossline (all inlines)" mode
   - User specifies the inline/crossline number
   - User clicks "Load Traces" to load the trace data

2. **Processing Phase** (analyze_data_page.py):
   - User selects which spectral descriptors to calculate
   - User clicks "Calculate Descriptors" to start processing
   - The application processes the section using GPU-accelerated functions
   - Results are stored in the session state

3. **Visualization Phase** (analyze_data_page.py):
   - The application displays the calculated attributes as section plots
   - Each selected descriptor is shown in a separate plot

## Implementation Details

### 1. Trace Loading

- In `select_area_page.py`, when the user clicks "Load Traces":
  - The application filters the SEG-Y header information to find traces that match the selected inline/crossline
  - For each matching trace, it loads the trace data using `load_trace_sample()`
  - The trace data is stored in `st.session_state.loaded_trace_data`
  - The trace indices are stored in `st.session_state.selected_indices`
  - The application navigates to the analysis page

### 2. Descriptor Calculation

In `analyze_data_page.py`, the `process_section_data()` function handles the calculation of spectral descriptors:

1. **Input Preparation**:
   - Extracts trace samples from the loaded trace data
   - Pads traces to ensure they all have the same length
   - Stacks traces into a 2D numpy array

2. **GPU Processing**:
   - Uses `dlogst_spec_descriptor_gpu_2d_chunked()` for efficient GPU processing
   - Processes the entire section in batches to manage GPU memory
   - Handles both 1D (single value per trace) and 2D (time series per trace) outputs

3. **WOSS Calculation**:
   - If WOSS is selected, ensures all required components (HFC, normalized dominant frequencies, and magnitude-voice slope) are calculated
   - Uses the `calculate_woss()` function to compute the WOSS attribute

### 3. Visualization

The visualization in `analyze_data_page.py` includes:

1. **Section Plots**:
   - Creates a plot for each selected descriptor
   - For 1D attributes (e.g., WOSS, spectral slope), shows a line plot
   - For 2D attributes (e.g., spectrograms), shows an image plot with a colorbar
   - Includes proper axis labels and titles

2. **Progress Feedback**:
   - Shows a progress bar during processing
   - Displays status messages to keep the user informed

## Key Functions

### `process_section_data(loaded_trace_data, selection_mode)`

Processes a section of trace data and calculates spectral descriptors.

**Parameters:**
- `loaded_trace_data`: List of dictionaries containing trace data
- `selection_mode`: String indicating the selection mode ("Single inline (all crosslines)" or "Single crossline (all inlines)")

**Returns:**
- A tuple containing:
  - A list of descriptor dictionaries (one per trace)
  - The number of skipped traces

### `render()` in analyze_data_page.py

Renders the analysis page UI, including:
- Section information display
- Output selection
- Descriptor calculation button
- Results visualization

## Session State Variables

The following session state variables are used:

- `loaded_trace_data`: List of trace data dictionaries
- `selected_indices`: List of trace indices for the selected section
- `selected_outputs`: List of selected output descriptors
- `calculated_descriptors`: List of calculated descriptor dictionaries
- `analysis_complete`: Boolean indicating if analysis is complete
- `batch_size`: Number of traces to process in each GPU batch
- `GPU_AVAILABLE`: Boolean indicating if GPU processing is available

## Error Handling

The implementation includes comprehensive error handling:
- Validates input parameters
- Handles missing or invalid data
- Provides informative error messages to the user
- Logs detailed error information for debugging

## Performance Considerations

- Uses GPU acceleration for efficient processing of large sections
- Processes data in batches to manage GPU memory usage
- Provides progress feedback for long-running operations
- Caches results in session state to avoid redundant calculations

## Dependencies

- NumPy: For numerical operations
- Matplotlib: For plotting
- Streamlit: For the web interface
- Custom GPU functions: For accelerated spectral analysis
