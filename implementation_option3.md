# Implementing Option 3: Single Crossline (All Inlines) in the WOSS Seismic Analysis Tool

This document explains the implementation of Option 3 - "Single crossline (all inlines)" in the WOSS Seismic Analysis Tool, detailing the workflow, code modifications, and key functionality.

## Overview

Option 3 allows users to select a single crossline and analyze all its intersection points with inlines. This is complementary to Option 2 (Single inline - all crosslines) and follows a similar workflow while focusing on crossline data.

## Implementation Steps

### 1. Adding the Mode to the Selection Options

The implementation started with ensuring "Single crossline (all inlines)" was available in the mode selection dropdown in `select_area_page.py`. This option was already included in the list:

```python
mode_options = [
    "By well markers",
    "Single inline (all crosslines)", 
    "Single crossline (all inlines)",  # Option 3
    "By inline/crossline section (AOI)",
    "By Polyline File Import"
]
```

### 2. UI for Crossline Number Selection

In `select_area_page.py`, UI elements were added to allow users to select a specific crossline number when "Single crossline (all inlines)" mode is selected:

```python
elif st.session_state.selection_mode == "Single crossline (all inlines)":
    st.subheader("Single Crossline Mode")
    if st.session_state.header_loader:
        # Get min and max crossline numbers
        min_crossline = int(np.min(st.session_state.header_loader.crosslines))
        max_crossline = int(np.max(st.session_state.header_loader.crosslines))

        # Default to previously selected crossline or min crossline
        default_crossline = st.session_state.selected_crossline if st.session_state.selected_crossline else min_crossline

        # Crossline number selection
        st.session_state.selected_crossline = st.number_input(
            f"Specify a crossline number ({min_crossline}-{max_crossline}):",
            min_value=min_crossline,
            max_value=max_crossline,
            value=default_crossline,
            step=1
        )

        # Batch size selection for GPU processing (if available)
        try:
            from common.ui_elements import get_suggested_batch_size
            GPU_AVAILABLE = 'dlogst_spec_descriptor_gpu' in globals() or 'dlogst_spec_descriptor_gpu' in st.session_state
            if GPU_AVAILABLE:
                suggested_batch, free_mb = get_suggested_batch_size()
                st.info(f"Estimated free GPU memory: {free_mb:.1f} MB. Suggested batch size: {suggested_batch}")
                st.session_state.batch_size = st.number_input(
                    "Batch size for GPU processing:",
                    min_value=10,
                    max_value=4000,
                    value=suggested_batch,
                    step=10,
                    help="Number of traces to process at once. Higher values use more GPU memory."
                )
        except Exception as gpu_err:
            logging.warning(f"Error configuring GPU options: {gpu_err}")
            st.warning("GPU processing options not available. Processing may be slower.")
    else:
        st.warning("SEG-Y headers not loaded yet.")
```

### 3. Trace Selection for the Chosen Crossline

In `app.py`, the main functionality for processing a selected crossline was implemented in the "select_traces" section:

```python
if st.session_state.selection_mode == "Single crossline (all inlines)":
    if st.session_state.header_loader and st.session_state.selected_crossline is not None:
        try:
            # Get the selected crossline number
            crossline_choice = st.session_state.selected_crossline

            # Filter traces by crossline
            crossline_mask = st.session_state.header_loader.crosslines == crossline_choice
            chosen_indices = st.session_state.header_loader.unique_indices[crossline_mask]

            if len(chosen_indices) == 0:
                st.error(f"No traces available for crossline {crossline_choice}.")
                # Add back button
            else:
                st.success(f"Found {len(chosen_indices)} traces for crossline {crossline_choice}.")
                st.session_state.selected_indices = chosen_indices.tolist()
```

### 4. Trace Data Loading

Once the crossline is selected, users can load all traces for that crossline:

```python
if not st.session_state.traces_loaded_crossline:
    if st.button("Load Traces for Processing", key="load_traces_crossline"):
        with st.spinner(f"Loading {len(chosen_indices)} traces for crossline {crossline_choice}..."):
            loaded_data_temp = []
            try:
                for idx in chosen_indices:
                    trace_sample = load_trace_sample(st.session_state.header_loader.source_file_path, idx)
                    if trace_sample is not None:
                        loaded_data_temp.append({
                            'trace_sample': trace_sample,
                            'trace_idx': idx
                        })
                st.session_state.loaded_trace_data = loaded_data_temp
                st.session_state.traces_loaded_crossline = True # Mark as loaded
                logging.info(f"Successfully loaded {len(loaded_data_temp)} traces for crossline {crossline_choice}.")
                st.rerun() # Rerun to update UI after loading
            except Exception as load_err:
                st.error(f"Error loading traces for crossline {crossline_choice}: {load_err}")
                logging.error(f"Trace loading failed for crossline {crossline_choice}: {load_err}", exc_info=True)
                st.session_state.loaded_trace_data = [] # Clear potentially partial data
                st.session_state.traces_loaded_crossline = False
else:
    st.success(f"✅ Traces loaded successfully. {len(st.session_state.loaded_trace_data)} traces are ready for processing.")
```

### 5. Output Selection

After loading the traces, users can select which seismic attributes to calculate:

```python
# Select outputs for 2D/3D mode
st.subheader("Select Outputs")

# Define available outputs for section view
AVAILABLE_OUTPUTS_SECTION = [
    "Input Signal",
    "Normalized dominant frequencies",
    "Spectral Slope", "Spectral Bandwidth",
    "Spectral Rolloff", "Mag*Voice Slope", "Spectral Decrease", "HFC", "WOSS"
]

# Use the section outputs list
available_outputs = AVAILABLE_OUTPUTS_SECTION

# Default to previously selected outputs or WOSS and Input Signal
default_outputs = st.session_state.selected_outputs if st.session_state.selected_outputs else ["Input Signal", "WOSS"]
# Filter default outputs to only include available outputs
default_outputs = [output for output in default_outputs if output in available_outputs]

st.session_state.selected_outputs = st.multiselect(
    "Select outputs to display:",
    options=available_outputs,
    default=default_outputs
)
```

### 6. Descriptor Calculation

When the user is ready, they can calculate the spectral descriptors for the selected crossline section:

```python
if st.button("Calculate Descriptors", key="calculate_descriptors_crossline", disabled=calculate_button_disabled):
    with st.spinner("Calculating spectral descriptors..."):
        try:
            if not st.session_state.loaded_trace_data:
                st.error("No trace data loaded to calculate descriptors.")
            else:
                # Prepare data for GPU function (assuming all traces have same length)
                data_2d = np.array([item['trace_sample'] for item in st.session_state.loaded_trace_data]) # [traces, samples] format

                # Use GPU function if available
                if GPU_AVAILABLE:
                    batch_size_gpu = st.session_state.get('batch_size', 512) # Get GPU batch size

                    # Map display names to internal names for GPU calculation
                    ATTR_NAME_MAP = {
                        "Input Signal": "data",
                        "Normalized dominant frequencies": "norm_fdom",
                        "Spectral Slope": "spec_slope",
                        "Spectral Bandwidth": "spec_bandwidth",
                        "Spectral Rolloff": "spec_rolloff",
                        "Mag*Voice Slope": "mag_voice_slope",
                        "Spectral Decrease": "spec_decrease",
                        "HFC": "hfc",
                        "WOSS": "WOSS"
                    }

                    # Convert selected outputs to internal names
                    internal_outputs_to_calculate = [ATTR_NAME_MAP.get(output, output) for output in st.session_state.selected_outputs]

                    # WOSS calculation requires specific components
                    required_for_woss = {"hfc", "norm_fdom", "mag_voice_slope"}
                    if "WOSS" in internal_outputs_to_calculate:
                        for comp in required_for_woss:
                            if comp not in internal_outputs_to_calculate:
                                internal_outputs_to_calculate.append(comp)

                    # Make sure 'data' is included as it's the original signal
                    if "data" not in internal_outputs_to_calculate:
                        internal_outputs_to_calculate.append("data")
                        
                    # Prepare list of attributes for GPU calculation
                    outputs_for_gpu_calc = [attr for attr in internal_outputs_to_calculate if attr != "WOSS"]

                    # Call the chunked GPU function
                    calculated_attribute_sections = dlogst_spec_descriptor_gpu_2d_chunked(
                        data_2d,
                        st.session_state.dt,
                        st.session_state.batch_size,
                        attributes_to_calculate=outputs_for_gpu_calc,
                        **spectral_params
                    )
```

### 7. WOSS Calculation (if selected)

If the user selects WOSS for calculation, it's calculated separately using the other components:

```python
# Calculate WOSS if selected
if "WOSS" in internal_outputs_to_calculate:
    # Get WOSS parameters
    epsilon = st.session_state.plot_settings.get('epsilon', 1e-4)
    fdom_exponent = st.session_state.plot_settings.get('fdom_exponent', 2.0)
    woss_section = np.zeros((calculated_attribute_sections['data'].shape))
    
    # Import WOSS calculation from processing
    from processing import calculate_woss
    
    # Calculate WOSS for each trace in the section
    for i in range(data_2d.shape[0]):
        trace_components = {
            'hfc': calculated_attribute_sections['hfc'][:, i],
            'norm_fdom': calculated_attribute_sections['norm_fdom'][:, i],
            'mag_voice_slope': calculated_attribute_sections['mag_voice_slope'][:, i]
        }
        woss_section[:, i] = calculate_woss(trace_components, st.session_state.plot_settings)
    calculated_attribute_sections["WOSS"] = woss_section
```

### 8. Storing Results and Proceeding to View

The results are stored in the session state and the app advances to the results view:

```python
st.session_state.calculated_descriptors = calculated_attribute_sections # Store the dict of 2D arrays
st.session_state.analysis_complete = True
st.success("Section descriptor calculation complete.")
st.session_state.current_step = "view_results"
st.rerun() # To update UI and show View Results
```

## Data Flow in Option 3 (Single Crossline Mode)

1. User selects "Single crossline (all inlines)" in the mode selection dropdown
2. User specifies a crossline number and GPU batch size (if GPU available)
3. The app identifies all trace indices where crossline matches the selected value
4. User loads the trace data for all identified traces
5. User selects which spectral attributes to calculate for the section
6. User initiates calculation of spectral descriptors
7. For GPU-enabled processing:
   - Data is assembled into a 2D array [traces × samples]
   - GPU-optimized functions calculate descriptors in batches
   - WOSS is calculated separately if selected
8. Results are stored and displayed in the view results page as 2D section plots

## Differences from Option 2 (Single Inline Mode)

While the functionality is similar, there are key differences:
- In Option 3, we filter traces by crossline number instead of inline number
- When displaying results, inline numbers are used for the x-axis (in Option 2, crossline numbers are used)
- The orientation of the resulting 2D section is orthogonal to that of Option 2

## Key Functions

1. `dlogst_spec_descriptor_gpu_2d_chunked`: GPU-accelerated function that processes a 2D array of traces in batches
2. `calculate_woss`: Calculates the WOSS attribute based on other spectral components
3. `load_trace_sample`: Loads seismic trace data from the SEG-Y file
4. `plot_multi_trace_section`: Visualizes the 2D section data (called in the view_results step)

## Session State Variables Used

- `st.session_state.selection_mode`: Stores the selected mode ("Single crossline (all inlines)")
- `st.session_state.selected_crossline`: Stores the selected crossline number
- `st.session_state.selected_indices`: Stores the indices of traces forming the crossline
- `st.session_state.traces_loaded_crossline`: Flag indicating if traces are loaded
- `st.session_state.loaded_trace_data`: List of dictionaries containing the loaded trace data
- `st.session_state.selected_outputs`: List of selected spectral attributes to calculate
- `st.session_state.batch_size`: GPU batch size for chunk processing
- `st.session_state.calculated_descriptors`: Dictionary of calculated 2D attribute arrays
- `st.session_state.analysis_complete`: Flag indicating calculation completion
