"""
WOSS Seismic Analysis Tool - Main Application Router

This module serves as the primary router for the WOSS Seismic Analysis Tool.
It imports page modules and calls their render() functions based on the current step
in the application workflow.
"""

import streamlit as st
import logging
import torch
import numpy as np

# Import common modules
from common.constants import APP_TITLE
from common.session_state import initialize_session_state, reset_state

# Import utility functions
from utils.data_utils import (
    get_well_marker_pairs_streamlit,
    get_nearest_trace_index,
    load_trace_sample
)
from utils.general_utils import get_suggested_batch_size

# Add calculate_woss function import or definition
def calculate_woss(trace_components, plot_settings):
    """
    Calculate WOSS (Weighted Oil Show Signature) from spectral components.
    
    Args:
        trace_components: dict with 'hfc', 'norm_fdom', 'mag_voice_slope' arrays
        plot_settings: dict with WOSS parameters including 'epsilon' and 'fdom_exponent'
    
    Returns:
        numpy array: WOSS values
    """
    epsilon = plot_settings.get('epsilon', 1e-4)
    fdom_exponent = plot_settings.get('fdom_exponent', 2.0)
    
    hfc = trace_components['hfc']
    norm_fdom = trace_components['norm_fdom']
    mag_voice_slope = trace_components['mag_voice_slope']
    
    # WOSS calculation formula
    woss = hfc * (norm_fdom ** fdom_exponent) * np.abs(mag_voice_slope) + epsilon
    
    return woss

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Configure page - THIS MUST BE THE FIRST STREAMLIT COMMAND
st.set_page_config(page_title=APP_TITLE, layout="wide")

# --- GPU Functions Import (from utils) ---

def _make_dummy_gpu_functions_global():
    """Helper to define dummy GPU functions in the global scope of app.py."""
    global dlogst_spec_descriptor_gpu, dlogst_spec_descriptor_gpu_2d_chunked, dlogst_spec_descriptor_gpu_2d_chunked_mag
    # These assignments make them global in this module's scope
    def dlogst_spec_descriptor_gpu_func(*args, **kwargs):
        raise NotImplementedError("GPU function dlogst_spec_descriptor_gpu not available.")
    def dlogst_spec_descriptor_gpu_2d_chunked_func(*args, **kwargs):
        raise NotImplementedError("GPU function dlogst_spec_descriptor_gpu_2d_chunked not available.")
    def dlogst_spec_descriptor_gpu_2d_chunked_mag_func(*args, **kwargs):
        raise NotImplementedError("GPU function dlogst_spec_descriptor_gpu_2d_chunked_mag not available.")
    dlogst_spec_descriptor_gpu = dlogst_spec_descriptor_gpu_func
    dlogst_spec_descriptor_gpu_2d_chunked = dlogst_spec_descriptor_gpu_2d_chunked_func
    dlogst_spec_descriptor_gpu_2d_chunked_mag = dlogst_spec_descriptor_gpu_2d_chunked_mag_func

@st.cache_resource
def initialize_gpu_functions():
    """
    Tries to import GPU-specific functions and returns their availability.
    Logs the import status once.
    Ensures GPU functions (real or dummy) are globally available in app.py.
    """
    global dlogst_spec_descriptor_gpu, dlogst_spec_descriptor_gpu_2d_chunked, dlogst_spec_descriptor_gpu_2d_chunked_mag
    gpu_available_status = False # Default to False

    # First check if CuPy is available
    try:
        import cupy as cp
        # Try to get GPU info to verify a working GPU
        free_bytes, total_bytes = cp.cuda.runtime.memGetInfo()
        free_mb = free_bytes / (1024 ** 2)
        total_mb = total_bytes / (1024 ** 2)
        logging.info(f"GPU detected with {free_mb:.1f} MB free out of {total_mb:.1f} MB total")

        # If we get here, CuPy is available and we have a working GPU
        try:
            from utils.dlogst_spec_descriptor_gpu import (
                dlogst_spec_descriptor_gpu as real_dlogst_spec_descriptor_gpu,
                dlogst_spec_descriptor_gpu_2d_chunked as real_dlogst_spec_descriptor_gpu_2d_chunked,
                dlogst_spec_descriptor_gpu_2d_chunked_mag as real_dlogst_spec_descriptor_gpu_2d_chunked_mag
            )
            # Assign real functions to global names
            dlogst_spec_descriptor_gpu = real_dlogst_spec_descriptor_gpu
            dlogst_spec_descriptor_gpu_2d_chunked = real_dlogst_spec_descriptor_gpu_2d_chunked
            dlogst_spec_descriptor_gpu_2d_chunked_mag = real_dlogst_spec_descriptor_gpu_2d_chunked_mag

            gpu_available_status = True
            logging.info("Successfully imported GPU spectral descriptor functions (cached)")
        except ImportError as e:
            logging.warning(f"CuPy is available but could not import GPU functions: {e}")
            _make_dummy_gpu_functions_global()
            gpu_available_status = False
    except (ImportError, Exception) as e:
        logging.warning(f"GPU not available: {e}")
        _make_dummy_gpu_functions_global()
        gpu_available_status = False

    # Set the GPU_AVAILABLE in session state for consistency across pages
    if 'session_state' in st.__dict__:
        st.session_state['GPU_AVAILABLE'] = gpu_available_status

    return gpu_available_status

# Initialize GPU functions and set availability
# Placeholders are defined before the call to satisfy linters and ensure names exist.
# These will be overwritten by initialize_gpu_functions with either real or dummy functions.
dlogst_spec_descriptor_gpu = None
dlogst_spec_descriptor_gpu_2d_chunked = None
dlogst_spec_descriptor_gpu_2d_chunked_mag = None
GPU_AVAILABLE = initialize_gpu_functions()

# Import page modules
from pages.load_data_page import render as render_load_data
from pages.configure_display_page import render as render_configure_display
from pages.select_area_page import render as render_select_area
from pages.analyze_data_page import render as render_analyze_data
from pages.export_results_page import render as render_export_results
from pages.test_gpu_fix_page import render as render_test_gpu_fix

# Initialize session state
initialize_session_state()

# Set application title
st.title(APP_TITLE)

# Initialize mode_selector_counter if it doesn't exist
if 'mode_selector_counter' not in st.session_state:
    st.session_state.mode_selector_counter = 0

# Now that we've initialized the app and set the page config first, we can add GPU status to sidebar
if GPU_AVAILABLE:
    st.sidebar.success("GPU functions loaded successfully.")
else:
    st.sidebar.warning("GPU functions not available. Processing will be slower.")

# Log current step for debugging
logging.info(f"Current step: {st.session_state.current_step}")
logging.info(f"Current selection_mode: {st.session_state.get('selection_mode')}")
logging.info(f"Mode selector: {st.session_state.get('mode_selector')}")

# Define application steps for sidebar navigation
APP_STEPS = {
    "1. Load Data": "load_data",
    "2. Configure Display": "configure_display",
    "3. Select Area": "select_mode",  # Use select_mode as in reference
    "4. Analyze Data": "analyze_data",
    "5. Export Results": "export_results",
    "Test GPU Fix": "test_gpu_fix",  # Add our test page
}

# Get the current step's display name for the radio button default
# This handles cases where current_step might be a sub-step like 'select_traces'
current_display_step = "1. Load Data" # Default
if st.session_state.current_step == "load_data":
    current_display_step = "1. Load Data"
elif st.session_state.current_step == "configure_display":
    current_display_step = "2. Configure Display"
elif st.session_state.current_step in ["select_mode", "select_traces"]:
    current_display_step = "3. Select Area"
elif st.session_state.current_step == "analyze_data":
    current_display_step = "4. Analyze Data"
elif st.session_state.current_step in ["view_results", "export_process", "download_export", "export_results"]:
    current_display_step = "5. Export Results"
elif st.session_state.current_step == "test_gpu_fix":
    current_display_step = "Test GPU Fix"


st.sidebar.header("Navigation")
selected_display_step = st.sidebar.radio(
    "Go to step:",
    options=list(APP_STEPS.keys()),
    index=list(APP_STEPS.keys()).index(current_display_step), # Set default based on current step
    key="sidebar_navigation"
)

# Update current_step if navigation changes
new_step = APP_STEPS[selected_display_step]
if st.session_state.current_step != new_step:
    # For steps that have sub-states, ensure we go to the primary state of that step
    if new_step == "select_mode":
        st.session_state.current_step = "select_mode"
    elif new_step == "export_results":
        st.session_state.current_step = "view_results"
    else:
        st.session_state.current_step = new_step
    st.rerun()


# Add "Start New Analysis" button to sidebar (visible on all pages)
st.sidebar.markdown("---")  # Add a separator
if st.sidebar.button("🔄 Start New Analysis", use_container_width=True, key="app_start_new_analysis_button"): # Added unique key
    reset_state() # This will set current_step back to 'load_data'
    st.success("Starting new analysis. All temporary data has been cleared.")
    st.rerun()

# Route to the appropriate page based on the current step
# The st.session_state.current_step is now managed by the sidebar navigation or page logic

# Log the current step and important session state variables for debugging
logging.info(f"Current step: {st.session_state.current_step}")
logging.info(f"display_params_configured: {st.session_state.get('display_params_configured')}")
logging.info(f"area_selected: {st.session_state.get('area_selected')}")

if st.session_state.current_step == "load_data":
    render_load_data()
elif st.session_state.current_step == "configure_display":
    render_configure_display()
elif st.session_state.current_step == "select_mode":
    render_select_area()  # Only mode selection
elif st.session_state.current_step == "select_traces":
    st.header("Step 3.5: Select Traces for Analysis")

    if st.session_state.selection_mode == "Single crossline (all inlines)":
        if st.session_state.header_loader and st.session_state.selected_crossline is not None:
            try:
                # Get the selected crossline number
                crossline_choice = st.session_state.selected_crossline

                # Filter traces by crossline
                crossline_mask = st.session_state.header_loader.crosslines == crossline_choice
                chosen_indices = st.session_state.header_loader.unique_indices[crossline_mask]

                if len(chosen_indices) == 0:
                    st.error(f"No traces available for crossline {crossline_choice}.")
                    if st.button("Back to Mode Selection"):
                        st.session_state.current_step = "select_mode"
                        st.rerun()
                else:
                    st.success(f"Found {len(chosen_indices)} traces for crossline {crossline_choice}.")
                    st.session_state.selected_indices = chosen_indices.tolist()

                    # Add a confirmation button to load traces
                    if 'traces_loaded_crossline' not in st.session_state:
                        st.session_state.traces_loaded_crossline = False

                    if not st.session_state.traces_loaded_crossline:
                        if st.button("Load Traces for Processing", key="load_traces_crossline"):
                            with st.spinner(f"Loading {len(chosen_indices)} traces for crossline {crossline_choice}..."):
                                loaded_data_temp = []
                                try:
                                    for idx in chosen_indices:
                                        trace_sample = load_trace_sample(st.session_state.header_loader.source_file_path, idx)
                                        if trace_sample is not None:
                                            loaded_data_temp.append({
                                                'trace_sample': trace_sample,
                                                'trace_idx': idx
                                            })
                                    st.session_state.loaded_trace_data = loaded_data_temp
                                    st.session_state.traces_loaded_crossline = True # Mark as loaded
                                    logging.info(f"Successfully loaded {len(loaded_data_temp)} traces for crossline {crossline_choice}.")
                                    st.rerun() # Rerun to update UI after loading
                                except Exception as load_err:
                                    st.error(f"Error loading traces for crossline {crossline_choice}: {load_err}")
                                    logging.error(f"Trace loading failed for crossline {crossline_choice}: {load_err}", exc_info=True)
                                    st.session_state.loaded_trace_data = [] # Clear potentially partial data
                                    st.session_state.traces_loaded_crossline = False
                    else:
                        st.success(f"✅ Traces loaded successfully. {len(st.session_state.loaded_trace_data)} traces are ready for processing.")

                    # Select outputs for 2D/3D mode
                    st.subheader("Select Outputs")

                    # Import constant from reference app
                    AVAILABLE_OUTPUTS_SECTION = [
                        "Input Signal",
                        "Normalized dominant frequencies",
                        "Spectral Slope", "Spectral Bandwidth",
                        "Spectral Rolloff", "Mag*Voice Slope", "Spectral Decrease", "HFC", "WOSS"
                    ]
                    
                    # Use the section outputs list
                    available_outputs = AVAILABLE_OUTPUTS_SECTION
                    
                    # Default to previously selected outputs or WOSS and Input Signal
                    default_outputs = st.session_state.selected_outputs if st.session_state.selected_outputs else ["Input Signal", "WOSS"]
                    # Filter default outputs to only include available outputs
                    default_outputs = [output for output in default_outputs if output in available_outputs]

                    st.session_state.selected_outputs = st.multiselect(
                        "Select outputs to display:",
                        options=available_outputs,
                        default=default_outputs
                    )

                    if not st.session_state.selected_outputs:
                        st.warning("Please select at least one output.")
                    else:
                        # Calculate spectral descriptors
                        st.subheader("Calculate Spectral Descriptors")

                        # Get spectral parameters from plot_settings
                        spectral_params = {
                            'use_band_limited': st.session_state.plot_settings.get('use_band_limited', False),
                            'shape': st.session_state.plot_settings.get('shape', 0.35),
                            'kmax': st.session_state.plot_settings.get('kmax', 120.0),
                            'int_val': st.session_state.plot_settings.get('int_val', 35.0),
                            'b1': st.session_state.plot_settings.get('b1', 5.0),
                            'b2': st.session_state.plot_settings.get('b2', 40.0),
                            'p_bandwidth': st.session_state.plot_settings.get('p_bandwidth', 2.0),
                            'roll_percent': st.session_state.plot_settings.get('roll_percent', 0.80)
                        }

                        # Calculate descriptors - only enable if traces are loaded
                        calculate_button_disabled = not st.session_state.traces_loaded_crossline
                        if calculate_button_disabled:
                            st.warning("Please load traces first before calculating descriptors.")

                        if st.button("Calculate Descriptors", key="calculate_descriptors_crossline", disabled=calculate_button_disabled):
                            with st.spinner("Calculating spectral descriptors..."):
                                try:
                                    if not st.session_state.loaded_trace_data:
                                        st.error("No trace data loaded to calculate descriptors.")
                                    else:
                                        # Prepare data for GPU function (assuming all traces have same length)
                                        data_2d = np.array([item['trace_sample'] for item in st.session_state.loaded_trace_data]) # [traces, samples] format

                                        # Use GPU function
                                        if GPU_AVAILABLE:
                                            batch_size_gpu = st.session_state.get('batch_size', 512) # Get GPU batch size

                                            # Map display names to internal names for GPU calculation
                                            ATTR_NAME_MAP = {
                                                "Input Signal": "data",
                                                "Normalized dominant frequencies": "norm_fdom",
                                                "Spectral Slope": "spec_slope",
                                                "Spectral Bandwidth": "spec_bandwidth",
                                                "Spectral Rolloff": "spec_rolloff",
                                                "Mag*Voice Slope": "mag_voice_slope",
                                                "Spectral Decrease": "spec_decrease",
                                                "HFC": "hfc",
                                                "WOSS": "WOSS"
                                            }

                                            # Convert selected outputs to internal names
                                            internal_outputs_to_calculate = [ATTR_NAME_MAP.get(output, output) for output in st.session_state.selected_outputs]

                                            # WOSS calculation requires specific components
                                            required_for_woss = {"hfc", "norm_fdom", "mag_voice_slope"}
                                            if "WOSS" in internal_outputs_to_calculate:
                                                for comp in required_for_woss:
                                                    if comp not in internal_outputs_to_calculate:
                                                        internal_outputs_to_calculate.append(comp)

                                            # Make sure 'data' is included as it's the original signal
                                            if "data" not in internal_outputs_to_calculate:
                                                internal_outputs_to_calculate.append("data")
                                                
                                            # Prepare list of attributes for GPU calculation
                                            outputs_for_gpu_calc = [attr for attr in internal_outputs_to_calculate if attr != "WOSS"]

                                            # Call the chunked GPU function
                                            calculated_attribute_sections = dlogst_spec_descriptor_gpu_2d_chunked(
                                                data_2d,
                                                st.session_state.dt,
                                                st.session_state.batch_size,
                                                attributes_to_calculate=outputs_for_gpu_calc,
                                                **spectral_params
                                            )
                                            
                                            # Calculate WOSS if selected
                                            if "WOSS" in internal_outputs_to_calculate:
                                                # Get WOSS parameters
                                                epsilon = st.session_state.plot_settings.get('epsilon', 1e-4)
                                                fdom_exponent = st.session_state.plot_settings.get('fdom_exponent', 2.0)
                                                woss_section = np.zeros((calculated_attribute_sections['data'].shape))
                                                
                                                # Calculate WOSS for each trace in the section
                                                for i in range(data_2d.shape[0]):
                                                    trace_components = {
                                                        'hfc': calculated_attribute_sections['hfc'][:, i],
                                                        'norm_fdom': calculated_attribute_sections['norm_fdom'][:, i],
                                                        'mag_voice_slope': calculated_attribute_sections['mag_voice_slope'][:, i]
                                                    }
                                                    woss_section[:, i] = calculate_woss(trace_components, st.session_state.plot_settings)
                                                calculated_attribute_sections["WOSS"] = woss_section

                                            st.session_state.calculated_descriptors = calculated_attribute_sections # Store the dict of 2D arrays
                                            st.session_state.analysis_complete = True
                                            st.success("Section descriptor calculation complete.")
                                            st.session_state.current_step = "view_results"
                                            st.rerun() # To update UI and show View Results
                                        else:
                                            st.error("GPU is not available. Cannot calculate descriptors for sections efficiently.")
                                except Exception as calc_err:
                                    st.error(f"Error calculating descriptors: {calc_err}")
                                    logging.error(f"Descriptor calculation failed (Crossline Mode): {calc_err}", exc_info=True)
                                    st.session_state.calculated_descriptors = []
                                    st.session_state.analysis_complete = False
            except Exception as e:
                st.error(f"Error processing crossline selection: {e}")
                logging.error(f"Crossline processing failed: {e}", exc_info=True)
        else:
            st.error("SEG-Y headers not loaded or crossline not selected.")
            if st.button("Back to Mode Selection"):
                st.session_state.current_step = "select_mode"
                st.rerun()

    elif st.session_state.selection_mode == "By well markers":
        if st.session_state.well_df is not None and not st.session_state.well_df.empty:
            # Get well marker pairs using the Streamlit-compatible function
            well_marker_dict = get_well_marker_pairs_streamlit(st.session_state.well_df)

            # Get selected well markers from session state
            selected_markers = st.session_state.get('selected_well_markers', [])

            # Filter pairs by selected markers if any
            filtered_pairs = []
            if selected_markers:
                filtered_pairs = [pair for pair in well_marker_dict.keys()
                               if any(marker in pair for marker in selected_markers)]
            else:
                filtered_pairs = list(well_marker_dict.keys())

            # If we have filtered pairs, show the selector
            if filtered_pairs:
                # Get previously selected pairs if any
                prev_selected = st.session_state.get('selected_well_marker_pairs', [])

                # Ensure prev_selected is a list
                if isinstance(prev_selected, str):
                    prev_selected = [prev_selected]

                # Filter out any previously selected pairs that are no longer in filtered_pairs
                valid_prev_selected = [p for p in prev_selected if p in filtered_pairs]

                # If no valid previous selection, default to first pair
                if not valid_prev_selected and filtered_pairs:
                    valid_prev_selected = [filtered_pairs[0]]

                # Update session state
                st.session_state.selected_well_marker_pairs = st.multiselect(
                    "Select Well-Marker Pairs:",
                    options=filtered_pairs,
                    default=valid_prev_selected,
                    key="well_marker_pairs_selector"
                )

                # Add a button to load traces for selected pairs
                if st.button("Load Traces for Selected Pairs"):
                    if st.session_state.selected_well_marker_pairs:
                        # Get the selected indices
                        selected_indices = [well_marker_dict[pair] for pair in st.session_state.selected_well_marker_pairs]
                        st.session_state.selected_indices = selected_indices

                        # Load trace data for the selected indices
                        with st.spinner("Loading trace data for selected well markers..."):
                            try:
                                loaded_trace_data = []
                                for idx in selected_indices:
                                    row = st.session_state.well_df.iloc[idx]
                                    trace_idx = get_nearest_trace_index(
                                        st.session_state.header_loader,
                                        row['X_UTME'],
                                        row['Y_UTMN']
                                    )
                                    if trace_idx is not None:
                                        # Get the SEG-Y file path from the header loader
                                        segy_path = getattr(st.session_state.header_loader, 'segy_path', None)
                                        if segy_path is None and hasattr(st.session_state, 'segy_file_info'):
                                            segy_path = st.session_state.segy_file_info.get('path')

                                        if segy_path is None:
                                            raise ValueError("SEG-Y file path not found in session state")

                                        trace_sample = load_trace_sample(segy_path, trace_idx)

                                        # Apply time window if needed
                                        time_min = st.session_state.plot_settings.get('time_min', 0.0)
                                        time_max = st.session_state.plot_settings.get('time_max', 4.0)
                                        if time_min > 0 or time_max < 4.0:
                                            # Convert time to sample indices
                                            sample_rate = st.session_state.dt  # Time between samples in seconds
                                            start_idx = int(time_min / sample_rate)
                                            end_idx = int(time_max / sample_rate)
                                            trace_sample = trace_sample[start_idx:end_idx]
                                        if trace_sample is not None:
                                            loaded_trace_data.append({
                                                'trace_idx': trace_idx,
                                                'trace_sample': trace_sample,
                                                'well_marker_name': f"{row['Well']} - {row['Surface']}",
                                                'x': row['X_UTME'],
                                                'y': row['Y_UTMN'],
                                                'well': row['Well'],
                                                'marker': row['Surface']
                                            })

                                if loaded_trace_data:
                                    st.session_state.loaded_trace_data = loaded_trace_data
                                    st.session_state.analysis_complete = False
                                    st.session_state.calculated_descriptors = None
                                    st.success(f"Successfully loaded {len(loaded_trace_data)} traces.")

                                    # Automatically proceed to analyze data page
                                    st.session_state.current_step = "analyze_data"
                                    st.rerun()
                                else:
                                    st.error("No valid trace data could be loaded for the selected well markers.")
                            except Exception as e:
                                st.error(f"Error loading trace data: {e}")
                                logging.error(f"Error loading trace data: {e}", exc_info=True)
                    else:
                        st.warning("Please select at least one well-marker pair.")
            else:
                st.warning("No well-marker pairs available for the selected markers.")
elif st.session_state.current_step in ["view_results", "export_process", "download_export", "export_results"]: # "export_results" is the key from APP_STEPS
    # Ensure sub-steps are handled correctly
    if st.session_state.current_step == "export_results": # Navigated via sidebar
        if not st.session_state.get('analysis_complete'): # Check if analysis is done
            st.session_state.current_step = "analyze_data" # Or a previous step if appropriate
            st.rerun()
            st.session_state.current_step = "view_results"
            # No rerun here, render_export_results will handle 'view_results'
    render_export_results()
elif st.session_state.current_step == "analyze_data":
    # Prerequisite checks for Analyze Data
    if not st.session_state.get('segy_file_info'):
        logging.warning("No SEG-Y file loaded. Redirecting to load_data step.")
        st.session_state.current_step = "load_data"
        st.rerun()
    elif not st.session_state.get('display_params_configured'):
        logging.warning("Display parameters not configured. Redirecting to configure_display step.")
        st.session_state.current_step = "configure_display"
        st.rerun()
    elif not st.session_state.get('area_selected'):
        logging.warning("Area not selected. Redirecting to select_mode step.")
        st.session_state.current_step = "select_mode" # or "select_area"
        st.rerun()
    else:
        logging.info("All prerequisites met for analyze_data. Rendering page.")
        render_analyze_data()
elif st.session_state.current_step == "test_gpu_fix":
    # No prerequisites for the test page
    logging.info("Rendering GPU fix test page.")
    render_test_gpu_fix()
else:
    # Default to load_data if current_step is not recognized (should be rare with radio button)
    st.session_state.current_step = "load_data"
    render_load_data()

# --- Implementation Notes for `pages.select_area_page.render_select_area` ---
# The following comments detail the requirements for "Step 3: Select Area", specifically for
# "Option 1: Well marker-pair selection". This logic should be implemented within the
# `render_select_area` function in `pages/select_area_page.py`.

# Requirement: Allow multiple well-marker pair selections.
# Reference: The Tkinter implementation's `select_well_marker_pairs` function in
#            `3D_WOSS_Main_Script_init.py` (lines 1007-1061) uses a multi-select listbox.

# Implementation Guide for `render_select_area` in `pages/select_area_page.py`:
#
# 1. When presenting well-marker pairs for selection (if this mode is chosen by the user):
#    - Use `st.multiselect` instead of `st.selectbox` or `st.radio`.
#    - Example:
#      `selected_marker_data_list = st.multiselect("Select well-marker pair(s):", options=available_marker_pairs_formatted, key="well_marker_multiselect")`
#      (where `available_marker_pairs_formatted` is a list of strings like "WellA - MarkerX")
#
# 2. `selected_marker_data_list` will be a list of the selected string representations.
#    You will need to map these back to the actual well and marker data.
#
# 3. Store the list of selected well-marker pair information (e.g., a list of dictionaries,
#    or a filtered DataFrame) in `st.session_state`. For example:
#    `st.session_state.selected_well_marker_pairs = processed_list_of_selected_pairs`
#
# 4. After selection, a button (e.g., "Complete Step 3 and Continue") should trigger the processing
#    of selected pairs and transition to the next step ("precompute_qc").
#
# 5. Ensure downstream processing (e.g., in `render_precompute_qc` or `render_analyze_data`)
#    can handle a list of well-marker pairs if this selection mode was used.
#    This might involve iterating through `st.session_state.selected_well_marker_pairs`.
#    The `precompute_qc` page should check `st.session_state.area_selected_mode` (e.g., 'well_markers', 'trace_range')
#    to determine how `st.session_state.selected_area_data` (or similar) should be interpreted.
#
# Example of processing the selection and transitioning within `render_select_area`:
#
#   `if 'well_data_df' in st.session_state and not st.session_state.well_data_df.empty:`
#       `well_df = st.session_state.well_data_df`
#       `# Format options for multiselect: "WellName - SurfaceName"`
#       `marker_options = sorted([f"{row['Well']} - {row['Surface']}" for _, row in well_df.iterrows()])`
#
#       `if not marker_options:`
#           `st.warning("No well marker pairs available to select from the loaded well data.")`
#       `else:`
#           `selected_labels = st.multiselect(`
#               `"Select well-marker pair(s) for analysis:",`
#               `marker_options,`
#               `default=st.session_state.get('previously_selected_marker_labels', []), # Optional: persist selection across reruns within the step`
#               `key="well_marker_multiselect"`
#           `)`
#           `st.session_state.previously_selected_marker_labels = selected_labels # Store for default`
#
#           `if st.button("✔️ Complete Step 3 and Continue to Pre-computation", key="complete_step3_button_well_markers"):`
#               `if selected_labels:`
#                   `selected_pairs_data = []`
#                   `for label in selected_labels:`
#                       `well_name, surface_name = label.split(" - ", 1)`
#                       `pair_df = well_df[(well_df['Well'] == well_name) & (well_df['Surface'] == surface_name)]`
#                       `if not pair_df.empty:`
#                           `# Store relevant data, e.g., the first match as a dictionary or specific fields`
#                           `selected_pairs_data.append(pair_df.iloc[0].to_dict())`
#                   `st.session_state.selected_well_marker_pairs = selected_pairs_data`
#                   `st.session_state.area_selected_details = {'type': 'well_markers', 'count': len(selected_pairs_data), 'labels': selected_labels}`
#                   `st.session_state.area_selected = True`
#                   `st.session_state.area_selected_mode = 'well_markers'`
#                   `st.success(f"{len(selected_pairs_data)} well-marker pair(s) selected. Proceeding to Pre-computation & QC.")`
#                   `st.session_state.current_step = "precompute_qc" # CRITICAL: Transition to next step`
#                   `# Clean up temporary selection state if needed`
#                   `if 'previously_selected_marker_labels' in st.session_state: del st.session_state.previously_selected_marker_labels`
#                   `st.rerun()`
#               `else:`
#                   `st.warning("Please select at least one well-marker pair to continue.")`
#           `elif selected_labels: # Show current selection if button not pressed yet`
#               `st.info(f"{len(selected_labels)} well-marker pair(s) currently selected. Click the button above to confirm and continue.")`
#           `else:`
#               `st.info("No well-marker pairs currently selected. Please make a selection and click the button to continue.")`
#   `else:`
#       `st.warning("Well data not loaded or empty. Please load well data in Step 1 to use this option.")`
#
# This note replaces the previous, more general comment block.
