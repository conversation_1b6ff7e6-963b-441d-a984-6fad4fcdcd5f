"""
Test Streamlit app to verify the fix for the logging issue in dlogst_spec_descriptor_gpu.py
"""

import torch
import streamlit as st
import numpy as np
import logging
import sys

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

st.title("GPU Function Test")

# Try to import GPU functions
try:
    import cupy as cp
    st.success("CuPy imported successfully")
    
    # Import the GPU functions
    from utils.dlogst_spec_descriptor_gpu import dlogst_spec_descriptor_gpu_2d_chunked
    st.success("GPU functions imported successfully")
    
    if st.button("Test GPU Function"):
        # Create some test data
        data = np.random.rand(10, 1000).astype(np.float32)
        dt = 0.004
        
        with st.spinner("Running GPU function..."):
            # Call the function with a specific batch size
            logging.info("Calling dlogst_spec_descriptor_gpu_2d_chunked with batch_size=5")
            st.info("Calling dlogst_spec_descriptor_gpu_2d_chunked with batch_size=5")
            
            try:
                result = dlogst_spec_descriptor_gpu_2d_chunked(
                    data,
                    dt,
                    batch_size=5
                )
                
                st.success("Function call successful!")
                st.write(f"Result keys: {list(result.keys())}")
                logging.info("Function call successful!")
                logging.info(f"Result keys: {list(result.keys())}")
            
            except Exception as e:
                st.error(f"Error in function call: {e}")
                logging.error(f"Error in function call: {e}")
                exc_type, exc_value, exc_traceback = sys.exc_info()
                logging.error(f"Exception type: {exc_type}")
                logging.error(f"Exception value: {exc_value}")
                import traceback
                tb_str = '\n'.join(traceback.format_tb(exc_traceback))
                logging.error(f"Traceback: {tb_str}")
                st.code(tb_str)

except ImportError as e:
    st.error(f"Error importing GPU modules: {e}")
    logging.error(f"Error importing GPU modules: {e}")
except Exception as e:
    st.error(f"Unexpected error: {e}")
    logging.error(f"Unexpected error: {e}")
    exc_type, exc_value, exc_traceback = sys.exc_info()
    logging.error(f"Exception type: {exc_type}")
    logging.error(f"Exception value: {exc_value}")
    import traceback
    tb_str = '\n'.join(traceback.format_tb(exc_traceback))
    logging.error(f"Traceback: {tb_str}")
    st.code(tb_str)
