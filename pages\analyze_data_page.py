
"""
Analyze Data Page for the WOSS Seismic Analysis Tool.

This module handles the UI rendering for analyzing the data.
It follows the principles outlined in rules.md, particularly regarding
the separation of concerns between UI and backend logic.
"""

import streamlit as st
import numpy as np
import pandas as pd
import logging
from tqdm import tqdm  # Import tqdm for progress bars

# Import common modules
from common.constants import (
    APP_TITLE, AVAILABLE_OUTPUTS_SINGLE, AVAILABLE_OUTPUTS_MULTI,
    AVAILABLE_OUTPUTS_SECTION
)
from common.session_state import initialize_session_state, reset_state
from common.ui_elements import get_suggested_batch_size

# Import utility functions
from utils.data_utils import get_well_marker_pairs, get_nearest_trace_index, load_trace_sample # Corrected import
from utils.general_utils import find_traces_near_polyline, parse_polyline_string
from utils.processing import calculate_woss
from utils.visualization import plot_trace_with_descriptors, plot_descriptor_section # Visualization imports

# Import GPU utility functions
from utils.gpu_utils import check_gpu_availability, log_gpu_info

# Import GPU functions if available
try:
    from utils.dlogst_spec_descriptor_gpu import (
        dlogst_spec_descriptor_gpu,
        dlogst_spec_descriptor_gpu_2d_chunked,
        dlogst_spec_descriptor_gpu_2d_chunked_mag
    )
    # Check if GPU is actually available and working
    GPU_AVAILABLE = check_gpu_availability()
    if GPU_AVAILABLE:
        logging.info("Successfully imported GPU spectral descriptor functions and verified GPU is working")
        # Log GPU information for debugging
        log_gpu_info()
    else:
        logging.warning("GPU functions imported but GPU is not available or not working correctly")
except ImportError as e:
    logging.warning(f"Could not import GPU functions: {e}")
    GPU_AVAILABLE = False
    # Define dummy functions
    def dlogst_spec_descriptor_gpu(*args, **kwargs):
        st.error("GPU function dlogst_spec_descriptor_gpu not available.")
        raise NotImplementedError("GPU function dlogst_spec_descriptor_gpu not available.")
    def dlogst_spec_descriptor_gpu_2d_chunked(*args, **kwargs):
        st.error("GPU function dlogst_spec_descriptor_gpu_2d_chunked not available.")
        raise NotImplementedError("GPU function dlogst_spec_descriptor_gpu_2d_chunked not available.")
    def dlogst_spec_descriptor_gpu_2d_chunked_mag(*args, **kwargs):
        st.error("GPU function dlogst_spec_descriptor_gpu_2d_chunked_mag not available.")
        raise NotImplementedError("GPU function dlogst_spec_descriptor_gpu_2d_chunked_mag not available.")

def render():
    """Render the analyze data page UI."""
    # Initialize session state if needed
    initialize_session_state()

    st.header("Step 4: Analyze Data")
    st.sidebar.header("Analysis Options")

    # Check if data is loaded
    if not st.session_state.header_loader:
        st.warning("Please load data first.")
        st.session_state.current_step = "load_data"
        st.rerun()
        return

    # Initialize precomputed_data as None
    precomputed_data = None

    # Check if we have precomputed data (from previous implementation)
    if st.session_state.get('precomputed_data_output') is not None:
        precomputed_data = st.session_state.get('precomputed_data_output')
        logging.info("Using existing precomputed data")
    else:
        # If no precomputed data, we'll work with raw data directly
        logging.info("No precomputed data found, will work with raw data directly")

    # Check if we have the HFC p95 value for WOSS calculation
    hfc_p95_value = st.session_state.get('hfc_p95')
    if hfc_p95_value is None:
        st.warning("HFC p95 value not found in session state. WOSS calculation might be inaccurate.")
        # We'll continue anyway, as calculate_woss has a fallback mechanism
    else:
        st.info(f"Using HFC p95 value: {hfc_p95_value:.3f} for WOSS calculation.")

    # Store GPU availability in session state
    st.session_state.GPU_AVAILABLE = GPU_AVAILABLE

    # Well Markers Mode
    if st.session_state.selection_mode == "By well markers":
        st.subheader("Selected Area Information") # Add subheader for clarity
        if st.session_state.well_df is not None and not st.session_state.well_df.empty:
            # Display information about the selected area from session state
            if st.session_state.get('selected_well_markers'):
                 st.write(f"Selected well markers: {', '.join(st.session_state.selected_well_markers)}")
            if st.session_state.get('selected_well_marker_pairs'):
                 st.write(f"Selected well-marker pairs: {', '.join(['{well} - {surface}'.format(well=pair.get('Well', 'Unknown'), surface=pair.get('Surface', 'Unknown')) for pair in st.session_state.selected_well_marker_pairs])}") # Display selected pairs
            if st.session_state.get('selected_indices'):
                 st.write(f"Number of selected traces: {len(st.session_state.selected_indices)}")

            # Use the loaded trace data from select_area_page.py
            loaded_trace_data = st.session_state.get('loaded_trace_data', [])

            if loaded_trace_data:
                st.success(f"Loaded {len(loaded_trace_data)} traces for analysis.")
            else:
                st.warning("No traces loaded for analysis from the selected well-marker pairs.")

            # Add a button to calculate descriptors
            st.markdown("---")
            st.markdown("### Next Steps:")
            st.markdown("1. Click **Calculate Descriptors** to process the selected traces and compute signal descriptors")
            st.markdown("2. Review the statistical summary that will appear")
            st.markdown("3. Click **View Results** to proceed to the results page for visualization and analysis options")

            if not st.session_state.get('GPU_AVAILABLE', False):
                st.error("GPU processing is required for this analysis mode, but no GPU is available. Please check your system configuration.")
            elif st.session_state.get('analysis_complete', False):
                st.info("✅ Analysis already complete. You can view the results below.")
            elif st.button("Calculate Descriptors", key="calculate_descriptors_well_markers", help="Process the selected traces and compute spectral descriptors"):
                if not loaded_trace_data:
                    st.warning("No traces loaded to calculate descriptors.")
                    return

                calculated_descriptors = []
                num_traces = len(loaded_trace_data)
                skipped_traces_count = 0

                progress_bar = st.progress(0)
                status_text = st.empty()
                status_text.text("Initializing descriptor calculation...")

                with st.spinner("Calculating spectral descriptors using GPU..."):
                    for i, trace_data in enumerate(loaded_trace_data):
                        well_marker_name = trace_data.get('well_marker_name', 'Unknown')
                        status_text.text(f"Processing trace {i+1}/{num_traces} for {well_marker_name} using GPU...")

                        descriptor = {}
                        try:
                            # Use the descriptor settings from plot_settings
                            descriptor_settings = {
                                'use_band_limited': st.session_state.plot_settings.get('use_band_limited', False),
                                'shape': st.session_state.plot_settings.get('shape', 0.35),
                                'kmax': st.session_state.plot_settings.get('kmax', 120.0),
                                'int_val': st.session_state.plot_settings.get('int_val', 35.0),
                                'b1': st.session_state.plot_settings.get('b1', 5.0),
                                'b2': st.session_state.plot_settings.get('b2', 40.0),
                                'p_bandwidth': st.session_state.plot_settings.get('p_bandwidth', 2.0),
                                'roll_percent': st.session_state.plot_settings.get('roll_percent', 0.80),
                                # 'epsilon': st.session_state.plot_settings.get('epsilon', 1e-4), # Epsilon handled in dlogst_spec_descriptor
                                # 'hfc_percentile': st.session_state.plot_settings.get('hfc_percentile', 95.0) # Not directly used by dlogst
                            }

                            # Ensure time and frequency limits are properly set (can remain as is)
                            if 'Time (Y-axis)' not in st.session_state.plot_settings and 'time_min' in st.session_state.plot_settings:
                                st.session_state.plot_settings['Time (Y-axis)'] = (
                                    st.session_state.plot_settings.get('time_min', 0.0),
                                    st.session_state.plot_settings.get('time_max', 4.0)
                                )
                            if 'Frequency' not in st.session_state.plot_settings and 'freq_min' in st.session_state.plot_settings:
                                nyquist_freq = 0.5 / st.session_state.dt if hasattr(st.session_state, 'dt') and st.session_state.dt > 0 else 125.0
                                st.session_state.plot_settings['Frequency'] = (
                                    st.session_state.plot_settings.get('freq_min', 0.0),
                                    st.session_state.plot_settings.get('freq_max', nyquist_freq)
                                )

                            trace_sample = trace_data['trace_sample']
                            fmax_value = len(trace_sample) // 2

                            logging.info(f"Attempting GPU processing for {well_marker_name}: length={len(trace_sample)}, fmax={fmax_value}")

                            # Prepare GPU-specific settings (excluding those not used by dlogst_spec_descriptor_gpu)
                            gpu_descriptor_settings = {
                                k: v for k, v in descriptor_settings.items()
                                # Add any other relevant parameters for dlogst_spec_descriptor_gpu if needed
                            }

                            # Verify trace_sample is valid before GPU call
                            if trace_sample is None or len(trace_sample) == 0:
                                raise ValueError(f"Invalid trace sample for {well_marker_name}: empty or None")

                            # Optional: NaN check before GPU, though dlogst_spec_descriptor_gpu should handle it
                            if np.isnan(trace_sample).any():
                                logging.warning(f"Trace sample for {well_marker_name} contains NaN values. GPU function will handle or return error.")
                                # trace_sample = np.nan_to_num(trace_sample, nan=0.0) # Decided to let GPU function handle

                            descriptor = dlogst_spec_descriptor_gpu(
                                trace_sample,
                                st.session_state.dt,
                                fmax=fmax_value,
                                **gpu_descriptor_settings
                            )

                            if descriptor is None:
                                raise ValueError("GPU function returned None instead of descriptor dictionary.")

                            # Check if the descriptor itself indicates an error (e.g. from input validation within dlogst_spec_descriptor_gpu)
                            # This depends on how dlogst_spec_descriptor_gpu signals errors (e.g., specific keys or NaN content)
                            # For now, we assume if it returns, it's either valid or contains NaNs that stats will handle.
                            # A more robust check could be if critical fields are all NaN.
                            if 'data' not in descriptor or not isinstance(descriptor['data'], np.ndarray): # Basic check
                                raise ValueError("GPU descriptor missing 'data' or invalid format.")


                            # Verify that critical descriptor keys are present (example)
                            # This check can be adjusted based on truly essential keys for subsequent steps.
                            expected_keys = ['hfc', 'norm_fdom', 'mag_voice_slope'] # Example critical keys
                            missing_critical_keys = [key for key in expected_keys if key not in descriptor or not isinstance(descriptor[key], np.ndarray) or descriptor[key].size == 0]

                            if missing_critical_keys:
                                # Check if all values in critical keys are NaN, if the key exists
                                all_nan_critical = True
                                for key in expected_keys:
                                    if key in descriptor and isinstance(descriptor[key], np.ndarray) and descriptor[key].size > 0:
                                        if not np.all(np.isnan(descriptor[key])):
                                            all_nan_critical = False
                                            break
                                    else: # Key missing or not an array or empty
                                        pass # Already covered by missing_critical_keys

                                if missing_critical_keys or all_nan_critical and not missing_critical_keys : # if keys are missing OR they exist but are all NaN
                                    # This indicates a failure in calculation for this trace.
                                    raise ValueError(f"Critical keys {missing_critical_keys if missing_critical_keys else expected_keys} missing or all NaN in GPU result for {well_marker_name}.")

                            logging.info(f"Successfully calculated GPU descriptors for {well_marker_name}")

                            if descriptor: # Ensure descriptor is not an empty dict from a caught error path
                                descriptor['well_marker_name'] = well_marker_name
                                descriptor['trace_idx'] = trace_data.get('trace_idx')
                            calculated_descriptors.append(descriptor)

                        except Exception as e:
                            logging.error(f"GPU descriptor calculation failed for trace {well_marker_name}: {e}", exc_info=True)
                            calculated_descriptors.append({'error': str(e), 'well_marker_name': well_marker_name, 'trace_idx': trace_data.get('trace_idx')})
                            st.warning(f"Could not process trace {well_marker_name} using GPU. Skipping. See logs for details.")
                            skipped_traces_count += 1

                        progress_bar.progress((i + 1) / num_traces)

                status_text.text("All traces processed.")
                progress_bar.empty()

                st.session_state.calculated_descriptors = calculated_descriptors

                # Calculate statistics for the descriptors IMMEDIATELY after descriptor calculation
                if calculated_descriptors:
                        # Initialize dictionaries to store statistics for all available outputs
                        stats = {}

                        # Create mapping between display names and internal descriptor keys
                        descriptor_mapping = {
                            # Signal data
                            "Input Signal": "data",

                            # Spectrograms and time-frequency representations
                            "Magnitude Spectrogram": "tf_map",
                            "Magnitude * Voice": "mag_voice",

                            # Frequency-related descriptors
                            "Normalized dominant frequencies": "norm_fdom",
                            "Normalized Dominant Frequency": "norm_fdom",
                            "Dominant Frequency": "fdom",
                            "Peak Frequency": "peak_freq",
                            "Spectral Centroid": "spec_centroid",

                            # Slope-related descriptors
                            "Spectral Slope": "spec_slope",
                            "Mag*Voice Slope": "mag_voice_slope",
                            "Voice Slope": "voice_slope",
                            "Slope Magnitude": "mag_voice_slope",

                            # Bandwidth and spectral shape descriptors
                            "Spectral Bandwidth": "spec_bandwidth",
                            "Spectral Rolloff": "spec_rolloff",
                            "Spectral Decrease": "spec_decrease",

                            # High-frequency content
                            "HFC": "hfc",
                            "Normalized HFC": "hfc",  # We'll normalize this manually

                            # Composite descriptors
                            "WOSS": "WOSS"
                        }

                        # Initialize stats for all available outputs
                        for output in AVAILABLE_OUTPUTS_SINGLE + AVAILABLE_OUTPUTS_MULTI:
                            if output not in stats:  # Initialize for all, including Mag Spec
                                stats[output] = {'values': []}

                        # Add custom stats for normalized values
                        stats['Normalized HFC'] = {'values': []}

                        # Pre-initialize additional statistics for spectrograms to avoid modifying dict during iteration
                        stats['Magnitude Spectrogram_max'] = {'values': []}
                        stats['Magnitude Spectrogram_median'] = {'values': []}
                        stats['Magnitude Spectrogram_std'] = {'values': []}
                        stats['Magnitude * Voice_max'] = {'values': []}
                        stats['Magnitude * Voice_median'] = {'values': []}
                        stats['Magnitude * Voice_std'] = {'values': []}

                        # Collect values for each descriptor
                        for desc_idx, desc in enumerate(calculated_descriptors): # Use enumerate for logging if needed
                            # Skip if it's an error placeholder or truly empty
                            if not desc or 'error' in desc:
                                if 'error' in desc:
                                     logging.warning(f"Skipping statistics calculation for trace {desc.get('well_marker_name', 'Unknown')} due to previous error: {desc['error']}")
                                else:
                                     logging.warning(f"Skipping statistics calculation for trace index {desc_idx} due to empty descriptor.")
                                continue

                            # Process each available output
                            for output_name, values_dict in stats.items():
                                # Get the corresponding internal key
                                internal_key = descriptor_mapping.get(output_name)

                                # Skip if no mapping or descriptor doesn't exist in current result
                                if not internal_key:
                                    continue
                                if internal_key not in desc:
                                    continue

                                # Enhanced statistics for 2D Spectrogram (handling 3D dimensional arrays for each trace)
                                if output_name == "Magnitude Spectrogram" and isinstance(desc[internal_key], np.ndarray) and desc[internal_key].ndim == 2:
                                    # Calculate multiple statistics for the 2D array
                                    spec_array = desc[internal_key]
                                    # Mean across all values
                                    mean_mag_spec = np.mean(spec_array)
                                    if not np.isnan(mean_mag_spec): values_dict['values'].append(mean_mag_spec)

                                    # These keys should already be pre-initialized before the loop
                                    # No need to check if they exist

                                    # Calculate additional statistics
                                    max_val = np.max(spec_array)
                                    median_val = np.median(spec_array)
                                    std_val = np.std(spec_array)

                                    # Store these statistics
                                    if not np.isnan(max_val): stats[f"{output_name}_max"]['values'].append(max_val)
                                    if not np.isnan(median_val): stats[f"{output_name}_median"]['values'].append(median_val)
                                    if not np.isnan(std_val): stats[f"{output_name}_std"]['values'].append(std_val)

                                # Enhanced statistics for Magnitude * Voice (handling 3D dimensional arrays for each trace)
                                elif output_name == "Magnitude * Voice" and isinstance(desc[internal_key], np.ndarray) and desc[internal_key].ndim == 2:
                                    # Calculate multiple statistics for the 2D array
                                    mag_voice_array = desc[internal_key]
                                    # Mean across all values
                                    mean_mag_voice = np.mean(mag_voice_array)
                                    if not np.isnan(mean_mag_voice): values_dict['values'].append(mean_mag_voice)

                                    # These keys should already be pre-initialized before the loop
                                    # No need to check if they exist

                                    # Calculate additional statistics
                                    max_val = np.max(mag_voice_array)
                                    median_val = np.median(mag_voice_array)
                                    std_val = np.std(mag_voice_array)

                                    # Store these statistics
                                    if not np.isnan(max_val): stats[f"{output_name}_max"]['values'].append(max_val)
                                    if not np.isnan(median_val): stats[f"{output_name}_median"]['values'].append(median_val)
                                    if not np.isnan(std_val): stats[f"{output_name}_std"]['values'].append(std_val)
                                # Special case for normalized HFC
                                elif output_name == 'Normalized HFC' and 'hfc' in desc:
                                    # Use the hfc_p95 value calculated during precomputation
                                    hfc_p95 = st.session_state.plot_settings.get('hfc_p95', 1.0)
                                    if hfc_p95 > 0:
                                        normalized_hfc = desc['hfc'] / hfc_p95
                                        values_dict['values'].extend(normalized_hfc)
                                    else:
                                        logging.warning("Invalid hfc_p95 value for normalization. Using max value instead.")
                                        hfc_max = np.max(np.abs(desc['hfc']))
                                        normalized_hfc = desc['hfc'] / (hfc_max + 1e-4)  # Add small epsilon
                                        values_dict['values'].extend(normalized_hfc)
                                # Handle WOSS calculation if not already present
                                elif output_name == 'WOSS' and 'WOSS' not in desc and all(k in desc for k in ['hfc', 'norm_fdom', 'mag_voice_slope']):
                                    # Ensure we're using the plot_settings with the hfc_p95 value from precomputation
                                    # Initialize woss_params, can be from plot_settings or an empty dict
                                    woss_params = st.session_state.get('plot_settings', {}).copy()

                                    # Get HFC p95 from session state (set in Step 2)
                                    hfc_p95_session_value = st.session_state.get('hfc_p95')

                                    if hfc_p95_session_value is not None:
                                        woss_params['hfc_p95'] = hfc_p95_session_value
                                        logging.info(f"Using HFC p95 from session state: {hfc_p95_session_value}")
                                    elif 'hfc_p95' not in woss_params:
                                        # Fallback: if not in session_state AND not in plot_settings, calculate from current data
                                        logging.warning("HFC p95 value not found in session state or plot_settings. Calculating from current data.")
                                        if 'hfc' in desc and isinstance(desc['hfc'], np.ndarray) and desc['hfc'].size > 0:
                                            hfc_percentile = woss_params.get('hfc_percentile', 95.0) # Default to 95 if not specified
                                            calculated_hfc_p95 = np.percentile(desc['hfc'], hfc_percentile)
                                            woss_params['hfc_p95'] = float(calculated_hfc_p95)
                                            logging.info(f"Calculated HFC p{hfc_percentile} from current data: {calculated_hfc_p95}")
                                        else:
                                            logging.warning("Could not calculate HFC p95 from current data as 'hfc' descriptor is missing or empty.")
                                            # calculate_woss itself has a fallback if hfc_p95 is still missing in woss_params

                                    # Log the parameters being used for WOSS calculation
                                    logging.info(f"WOSS calculation parameters: hfc_p95={woss_params.get('hfc_p95', 'Not set/Fallback in calculate_woss')}, epsilon={woss_params.get('epsilon', 1e-4)}")
                                    woss = calculate_woss(desc, woss_params)
                                    values_dict['values'].extend(woss)
                                # Regular case (for 1D arrays)
                                elif isinstance(desc[internal_key], np.ndarray) and desc[internal_key].ndim == 1:
                                    values_dict['values'].extend(desc[internal_key])

                        # Calculate statistics for each descriptor
                        for desc_name, desc_stats in stats.items():
                            if desc_stats['values']:
                                values = np.array(desc_stats['values'])
                                desc_stats['min'] = np.min(values)
                                desc_stats['max'] = np.max(values)
                                desc_stats['p5'] = np.percentile(values, 5)
                                desc_stats['p90'] = np.percentile(values, 90)

                                # Calculate and store HFC p95 value for WOSS calculation
                                if desc_name == 'HFC':
                                    hfc_percentile = st.session_state.plot_settings.get('hfc_percentile', 95.0)
                                    hfc_p95 = np.percentile(values, hfc_percentile)
                                    st.session_state.plot_settings['hfc_p95'] = float(hfc_p95)
                                    logging.info(f"Calculated and stored HFC p{hfc_percentile} value: {hfc_p95}")
                            else:
                                desc_stats['min'] = 'N/A'
                                desc_stats['max'] = 'N/A'
                                desc_stats['p5'] = 'N/A'
                                desc_stats['p90'] = 'N/A'

                        # Display the statistics in an expander
                        with st.expander("📊 Descriptor Statistics Summary", expanded=True):
                            st.markdown("### Signal Descriptor Statistics")
                            st.markdown("The following statistics have been calculated for the selected traces:")

                            # Group descriptors into categories for better organization
                            categories = {
                                "Primary Descriptors": [
                                    "Slope Magnitude", "Normalized HFC", "Normalized Dominant Frequencies",
                                    "WOSS", "Normalized Dominant Frequency", "Mag*Voice Slope"
                                ],
                                "Spectral Properties": [
                                    "Spectral Slope", "Spectral Bandwidth", "Spectral Rolloff",
                                    "Spectral Decrease", "Magnitude * Voice"
                                ],
                                "Signal Characteristics": [
                                    "Input Signal", "HFC", "Magnitude Spectrogram"
                                ],
                                "Spectrogram Statistics": [
                                    "Magnitude Spectrogram_max", "Magnitude Spectrogram_median", "Magnitude Spectrogram_std",
                                    "Magnitude * Voice_max", "Magnitude * Voice_median", "Magnitude * Voice_std"
                                ],
                                "Other Descriptors": []  # For any remaining descriptors
                            }

                            # Assign each descriptor to a category
                            categorized_stats = {cat: [] for cat in categories.keys()}

                            for desc_name, desc_stats in stats.items():
                                if not desc_stats['values']:  # Skip empty descriptors
                                    continue

                                # Find which category this descriptor belongs to
                                assigned = False
                                for cat_name, cat_descriptors in categories.items():
                                    if desc_name in cat_descriptors:
                                        if isinstance(desc_stats['min'], (int, float)):
                                            categorized_stats[cat_name].append({
                                                "Descriptor": desc_name,
                                                "Min": desc_stats['min'],  # Keep as numeric
                                                "Max": desc_stats['max'],  # Keep as numeric
                                                "P5": desc_stats['p5'],    # Keep as numeric
                                                "P90": desc_stats['p90']   # Keep as numeric
                                            })
                                        else:
                                            categorized_stats[cat_name].append({
                                                "Descriptor": desc_name,
                                                "Min": desc_stats['min'],
                                                "Max": desc_stats['max'],
                                                "P5": desc_stats['p5'],
                                                "P90": desc_stats['p90']
                                            })
                                        assigned = True
                                        break

                                # If not assigned to any specific category, put in "Other Descriptors"
                                if not assigned and desc_stats['values']:
                                    if isinstance(desc_stats['min'], (int, float)):
                                        categorized_stats["Other Descriptors"].append({
                                            "Descriptor": desc_name,
                                            "Min": desc_stats['min'],  # Keep as numeric
                                            "Max": desc_stats['max'],  # Keep as numeric
                                            "P5": desc_stats['p5'],    # Keep as numeric
                                            "P90": desc_stats['p90']   # Keep as numeric
                                        })
                                    else:
                                        categorized_stats["Other Descriptors"].append({
                                            "Descriptor": desc_name,
                                            "Min": desc_stats['min'],
                                            "Max": desc_stats['max'],
                                            "P5": desc_stats['p5'],
                                            "P90": desc_stats['p90']
                                        })

                            # Display tables by category
                            for cat_name, cat_stats in categorized_stats.items():
                                if cat_stats:  # Only show categories with data
                                    st.subheader(cat_name)
                                    # Create DataFrame with numeric values
                                    df = pd.DataFrame(cat_stats)

                                    # Format the DataFrame for display with 4 decimal places
                                    # but keep the underlying data as numeric
                                    formatted_df = df.style.format({
                                        "Min": "{:.4f}",
                                        "Max": "{:.4f}",
                                        "P5": "{:.4f}",
                                        "P90": "{:.4f}"
                                    }, na_rep="N/A")

                                    st.table(formatted_df)

                            # Add explanation
                            st.markdown("""
                            **Note:**
                            - **Min/Max**: Absolute minimum and maximum values across all traces
                            - **P5**: 5th percentile value (95% of values are above this)
                            - **P90**: 90th percentile value (10% of values are above this)
                            """)

                        # Store the statistics in session state for later use
                        st.session_state.descriptor_statistics = stats

                # Set analysis complete flag AFTER all processing (descriptors + statistics)
                st.session_state.analysis_complete = True

                # Show completion message
                if skipped_traces_count > 0:
                    st.warning(f"Descriptor calculation finished. {skipped_traces_count} trace(s) could not be processed due to GPU errors. Please check logs for details.")
                else:
                    st.success("✅ Descriptor calculation and statistical analysis complete!")

            # Proceed to results - only show this button after descriptors AND statistics are calculated
            if st.session_state.analysis_complete:
                st.markdown("---")
                st.success("✅ Descriptor calculation is complete. You can now view the detailed results.")
                if st.button("View Results", key="view_results_button_main", help="Open the detailed analysis report"):
                    # Track the transition to prevent loops
                    st.session_state.last_step = 'analyze_data'
                    st.session_state.current_step = "view_results"
                    logging.info("Transitioning from analyze_data to view_results for well markers mode")
                    st.rerun()

        else:
            st.warning("Please select at least one well-marker pair.")
    else:
        st.error("No well data available. Please go back and upload well data.")
        if st.button("Back to Mode Selection"):
            st.session_state.current_step = "select_mode"
            st.rerun()

    # AOI Mode
    if st.session_state.selection_mode == "By inline/crossline section (AOI)":
        # Process AOI selection
        st.info("Processing AOI selection...")

        # Get the selected AOI
        inline_min = st.session_state.aoi_inline_min
        inline_max = st.session_state.aoi_inline_max
        xline_min = st.session_state.aoi_xline_min
        xline_max = st.session_state.aoi_xline_max

        # Find traces within the AOI
        with st.spinner("Finding traces within the AOI..."):
            try:
                # Get header dataframe
                headers_df = pd.DataFrame({
                    'inline': st.session_state.header_loader.inlines,
                    'crossline': st.session_state.header_loader.crosslines,
                    'x': st.session_state.header_loader.x_coords,
                    'y': st.session_state.header_loader.y_coords
                })
                headers_df['trace_idx'] = st.session_state.header_loader.unique_indices

                # Filter by inline/crossline range
                aoi_df = headers_df[
                    (headers_df['inline'] >= inline_min) &
                    (headers_df['inline'] <= inline_max) &
                    (headers_df['crossline'] >= xline_min) &
                    (headers_df['crossline'] <= xline_max) # Completed the condition
                ]

                st.session_state.selected_indices = aoi_df['trace_idx'].tolist()
                st.success(f"Found {len(st.session_state.selected_indices)} traces within the specified AOI.")

                # Add a button to proceed to analysis
                st.markdown("---")
                st.markdown("### Next Steps:")
                st.markdown("1. Click **Calculate Descriptors** to process the selected traces and compute signal descriptors")
                st.markdown("2. Review the statistical summary that will appear")
                st.markdown("3. Click **View Results** to proceed to the results page for visualization and analysis options")

                if st.button("Calculate Descriptors", key="calculate_descriptors_aoi", help="Process the selected traces and compute spectral descriptors"):
                    # Calculate descriptors for the selected AOI traces
                    with st.spinner("Calculating spectral descriptors for AOI..."):
                        # Load trace data for the selected indices
                        loaded_trace_data = []
                        max_len = 0
                        for trace_idx in st.session_state.selected_indices:
                            # Check if we have pre-computed data for this trace
                            if precomputed_data:
                                precomputed_item = next((item for item in precomputed_data if item.get('trace_idx') == trace_idx), None)
                                if precomputed_item and 'processed_trace' in precomputed_item:
                                    # Use the processed trace from pre-computation
                                    trace_sample = precomputed_item['processed_trace']
                                    logging.info(f"Using pre-computed data for trace {trace_idx}")
                                else:
                                    # Fallback to loading the original trace
                                    trace_sample = load_trace_sample(st.session_state.header_loader.source_file_path, trace_idx)
                                    logging.info(f"Pre-computed data not found for trace {trace_idx}, using original data")
                            else:
                                # No precomputed data available, load the original trace
                                trace_sample = load_trace_sample(st.session_state.header_loader.source_file_path, trace_idx)
                                logging.info(f"Loading original data for trace {trace_idx} (no precomputation)")

                            loaded_trace_data.append({'trace_sample': trace_sample, 'trace_idx': trace_idx})
                            max_len = max(max_len, len(trace_sample))

                        # Pad traces if necessary
                        if max_len > 0:
                            for item in loaded_trace_data:
                                if len(item['trace_sample']) < max_len:
                                    pad_width = max_len - len(item['trace_sample'])
                                    item['trace_sample'] = np.pad(item['trace_sample'], (0, pad_width), 'constant')

                        # Stack traces into a 2D array for batch processing
                        if loaded_trace_data:
                            traces_array = np.stack([t['trace_sample'] for t in loaded_trace_data]).astype(np.float32)
                            fmax_calc = max_len // 2 if max_len > 0 else 250

                            # Use the descriptor settings from plot_settings
                            descriptor_settings = {
                                'use_band_limited': st.session_state.plot_settings.get('use_band_limited', False),
                                'shape': st.session_state.plot_settings.get('shape', 0.35),
                                'kmax': st.session_state.plot_settings.get('kmax', 120.0),
                                'int_val': st.session_state.plot_settings.get('int_val', 35.0),
                                'b1': st.session_state.plot_settings.get('b1', 5.0),
                                'b2': st.session_state.plot_settings.get('b2', 40.0),
                                'p_bandwidth': st.session_state.plot_settings.get('p_bandwidth', 2.0),
                                'roll_percent': st.session_state.plot_settings.get('roll_percent', 0.80),
                                'epsilon': st.session_state.plot_settings.get('epsilon', 1e-4),
                                'hfc_percentile': st.session_state.plot_settings.get('hfc_percentile', 95.0)
                            }

                            try:
                                if st.session_state.get('GPU_AVAILABLE', False):
                                    try:
                                        # Remove WOSS-specific parameters for GPU function
                                        gpu_descriptor_settings = {
                                            k: v for k, v in descriptor_settings.items()
                                            if k not in ['epsilon', 'fdom_exponent', 'hfc_p95', 'hfc_percentile']
                                        }

                                        # Log parameters for debugging
                                        logging.info(f"Calling dlogst_spec_descriptor_gpu_2d_chunked_mag with parameters: dt={st.session_state.dt}, fmax={fmax_calc}, batch_size={st.session_state.get('batch_size', 512)}, settings={gpu_descriptor_settings}")

                                        # Use the 2D chunked GPU function
                                        calculated_descriptors_dict = dlogst_spec_descriptor_gpu_2d_chunked_mag(
                                            traces_array,
                                            st.session_state.dt,
                                            fmax=fmax_calc,
                                            batch_size=st.session_state.get('batch_size', 512), # Use batch size from session state
                                            **gpu_descriptor_settings
                                        )

                                        # Verify that we got results back
                                        if not calculated_descriptors_dict:
                                            st.error("GPU calculation returned empty results. Check logs for details.")
                                            logging.error("GPU calculation returned empty results.")
                                            raise ValueError("Empty descriptor results from GPU calculation")

                                        # Log the keys we got back
                                        logging.info(f"Received descriptor keys: {list(calculated_descriptors_dict.keys())}")

                                        # Convert dictionary of arrays to list of dictionaries
                                        num_traces_in_batch = traces_array.shape[0]
                                        calculated_descriptors = [{} for _ in range(num_traces_in_batch)]
                                        for key, value_array in calculated_descriptors_dict.items():
                                            if isinstance(value_array, np.ndarray) and value_array.shape[0] == num_traces_in_batch:
                                                for trace_idx in range(num_traces_in_batch):
                                                    calculated_descriptors[trace_idx][key] = value_array[trace_idx]
                                            # Handle 2D arrays like 'mag' and 'mag_voice'
                                            elif isinstance(value_array, np.ndarray) and value_array.ndim == 3 and value_array.shape[0] == num_traces_in_batch:
                                                for trace_idx in range(num_traces_in_batch):
                                                    calculated_descriptors[trace_idx][key] = value_array[trace_idx]
                                            else:
                                                logging.warning(f"Shape mismatch for descriptor '{key}' in AOI processing. Expected {num_traces_in_batch} traces, got {value_array.shape if isinstance(value_array, np.ndarray) else 'not an array'}. Skipping this descriptor.")

                                        # Verify that descriptors contain expected keys
                                        expected_keys = ['data', 'peak_freq', 'spec_centroid', 'fdom', 'norm_fdom', 'hfc', 'mag_voice_slope']
                                        if calculated_descriptors:
                                            missing_keys = [key for key in expected_keys if key not in calculated_descriptors[0]]
                                            if missing_keys:
                                                logging.warning(f"Descriptors are missing expected keys: {missing_keys}")
                                                st.warning(f"Descriptor calculation incomplete. Missing: {', '.join(missing_keys)}")

                                    except Exception as e:
                                        logging.error(f"Error in GPU batch processing: {e}", exc_info=True)
                                        st.error(f"GPU batch processing error: {str(e)}")
                                        # Fall back to individual processing
                                        st.warning("Falling back to individual trace processing...")
                                        calculated_descriptors = []
                                        for trace_item in tqdm(loaded_trace_data, desc="Processing traces individually"):
                                            try:
                                                # Remove WOSS-specific parameters for GPU function
                                                gpu_descriptor_settings = {
                                                    k: v for k, v in descriptor_settings.items()
                                                    if k not in ['epsilon', 'fdom_exponent', 'hfc_p95', 'hfc_percentile']
                                                }

                                                descriptor = dlogst_spec_descriptor_gpu(
                                                    trace_item['trace_sample'],
                                                    st.session_state.dt,
                                                    fmax=len(trace_item['trace_sample']) // 2,
                                                    **gpu_descriptor_settings
                                                )
                                                calculated_descriptors.append(descriptor)
                                            except Exception as e_inner:
                                                logging.error(f"Error processing trace {trace_item['trace_idx']}: {e_inner}")
                                                calculated_descriptors.append({})

                                else:
                                    st.warning("GPU not available. Processing AOI on CPU (very slow)...")
                                    # Fallback to processing traces individually on CPU
                                    calculated_descriptors = []
                                    try:
                                        # Try to import CPU implementation if available
                                        from utils.dlogst_spec_descriptor_cpu import dlogst_spec_descriptor_cpu

                                        for trace_item in tqdm(loaded_trace_data, desc="Processing traces on CPU"):
                                            try:
                                                descriptor = dlogst_spec_descriptor_cpu(
                                                    trace_item['trace_sample'],
                                                    st.session_state.dt,
                                                    fmax=len(trace_item['trace_sample']) // 2,
                                                    **descriptor_settings
                                                )
                                                calculated_descriptors.append(descriptor)
                                            except Exception as e:
                                                logging.error(f"Error calculating descriptor for trace {trace_item['trace_idx']} on CPU: {e}")
                                                calculated_descriptors.append({})
                                    except ImportError:
                                        st.error("CPU implementation not available. Cannot calculate descriptors.")
                                        # Create empty descriptors as fallback
                                        calculated_descriptors = [{} for _ in range(len(loaded_trace_data))]
                                    except Exception as e:
                                        logging.error(f"Error in CPU processing setup: {e}", exc_info=True)
                                        st.error(f"CPU processing error: {str(e)}")
                                        calculated_descriptors = [{} for _ in range(len(loaded_trace_data))]

                                # Check if we have valid descriptors
                                valid_descriptors = [d for d in calculated_descriptors if d and len(d) > 0]
                                if valid_descriptors:
                                    st.session_state.calculated_descriptors = calculated_descriptors
                                    st.session_state.analysis_complete = True
                                    st.success(f"Descriptor calculation complete for AOI. {len(valid_descriptors)} of {len(calculated_descriptors)} traces processed successfully.")
                                else:
                                    st.error("No valid descriptors were calculated. Please check the logs for errors.")
                                    st.session_state.calculated_descriptors = []
                                    st.session_state.analysis_complete = False

                                # Calculate statistics for the descriptors (same logic as well markers mode)
                                if calculated_descriptors:
                                    # Initialize dictionaries to store statistics for all available outputs
                                    stats = {}

                                    # Create mapping between display names and internal descriptor keys
                                    descriptor_mapping = {
                                        # Signal data
                                        "Input Signal": "data",

                                        # Spectrograms and time-frequency representations
                                        "Magnitude Spectrogram": "mag", # Use 'mag' key
                                        "Magnitude * Voice": "mag_voice", # Use 'mag_voice' key

                                        # Frequency-related descriptors
                                        "Normalized dominant frequencies": "norm_fdom",
                                        "Normalized Dominant Frequency": "norm_fdom",
                                        "Dominant Frequency": "fdom",
                                        "Peak Frequency": "peak_freq",
                                        "Spectral Centroid": "spec_centroid",

                                        # Slope-related descriptors
                                        "Spectral Slope": "spec_slope",
                                        "Mag*Voice Slope": "mag_voice_slope",
                                        "Voice Slope": "voice_slope",
                                        "Slope Magnitude": "mag_voice_slope",

                                        # Bandwidth and spectral shape descriptors
                                        "Spectral Bandwidth": "spec_bandwidth",
                                        "Spectral Rolloff": "spec_rolloff",
                                        "Spectral Decrease": "spec_decrease",

                                        # High-frequency content
                                        "HFC": "hfc",
                                        "Normalized HFC": "hfc",  # We'll normalize this manually

                                        # Composite descriptors
                                        "WOSS": "WOSS"
                                    }

                                    # Initialize stats for all available outputs
                                    for output in AVAILABLE_OUTPUTS_SINGLE + AVAILABLE_OUTPUTS_MULTI:
                                        if output not in stats:
                                            stats[output] = {'values': []}

                                    # Add custom stats for normalized values
                                    stats['Normalized HFC'] = {'values': []}

                                    # Pre-initialize additional statistics for spectrograms to avoid modifying dict during iteration
                                    stats['Magnitude Spectrogram_max'] = {'values': []}
                                    stats['Magnitude Spectrogram_median'] = {'values': []}
                                    stats['Magnitude Spectrogram_std'] = {'values': []}
                                    stats['Magnitude * Voice_max'] = {'values': []}
                                    stats['Magnitude * Voice_median'] = {'values': []}
                                    stats['Magnitude * Voice_std'] = {'values': []}

                                    # Collect values for each descriptor
                                    for desc in calculated_descriptors:
                                        if not desc:  # Skip empty descriptors
                                            continue

                                        # Process each available output
                                        for output_name, values_dict in stats.items():
                                            # Get the corresponding internal key
                                            internal_key = descriptor_mapping.get(output_name)

                                            # Skip if no mapping or descriptor doesn't exist in current result
                                            if not internal_key:
                                                continue
                                            if internal_key not in desc:
                                                continue

                                            # Enhanced statistics for 2D Spectrogram (handling 3D dimensional arrays for each trace)
                                            if output_name == "Magnitude Spectrogram" and isinstance(desc[internal_key], np.ndarray) and desc[internal_key].ndim == 2:
                                                # Calculate multiple statistics for the 2D array
                                                spec_array = desc[internal_key]
                                                # Mean across all values
                                                mean_mag_spec = np.mean(spec_array)
                                                if not np.isnan(mean_mag_spec): values_dict['values'].append(mean_mag_spec)

                                                # These keys should already be pre-initialized before the loop
                                                # No need to check if they exist

                                                # Calculate additional statistics
                                                max_val = np.max(spec_array)
                                                median_val = np.median(spec_array)
                                                std_val = np.std(spec_array)

                                                # Store these statistics
                                                if not np.isnan(max_val): stats[f"{output_name}_max"]['values'].append(max_val)
                                                if not np.isnan(median_val): stats[f"{output_name}_median"]['values'].append(median_val)
                                                if not np.isnan(std_val): stats[f"{output_name}_std"]['values'].append(std_val)

                                            # Enhanced statistics for Magnitude * Voice (handling 3D dimensional arrays for each trace)
                                            elif output_name == "Magnitude * Voice" and isinstance(desc[internal_key], np.ndarray) and desc[internal_key].ndim == 2:
                                                # Calculate multiple statistics for the 2D array
                                                mag_voice_array = desc[internal_key]
                                                # Mean across all values
                                                mean_mag_voice = np.mean(mag_voice_array)
                                                if not np.isnan(mean_mag_voice): values_dict['values'].append(mean_mag_voice)

                                                # These keys should already be pre-initialized before the loop
                                                # No need to check if they exist

                                                # Calculate additional statistics
                                                max_val = np.max(mag_voice_array)
                                                median_val = np.median(mag_voice_array)
                                                std_val = np.std(mag_voice_array)

                                                # Store these statistics
                                                if not np.isnan(max_val): stats[f"{output_name}_max"]['values'].append(max_val)
                                                if not np.isnan(median_val): stats[f"{output_name}_median"]['values'].append(median_val)
                                                if not np.isnan(std_val): stats[f"{output_name}_std"]['values'].append(std_val)
                                            # Special case for normalized HFC
                                            elif output_name == 'Normalized HFC' and 'hfc' in desc:
                                                # Use the hfc_p95 value calculated during precomputation
                                                hfc_p95 = st.session_state.get('hfc_p95', 1.0) # Get from session state, default to 1.0 if not found
                                                if hfc_p95 > 0:
                                                    normalized_hfc = desc['hfc'] / hfc_p95
                                                    values_dict['values'].extend(normalized_hfc)
                                                else:
                                                    logging.warning("Invalid hfc_p95 value for normalization. Using max value instead.")
                                                    hfc_max = np.max(np.abs(desc['hfc']))
                                                    normalized_hfc = desc['hfc'] / (hfc_max + 1e-4)  # Add small epsilon
                                                    values_dict['values'].extend(normalized_hfc)
                                            # Handle WOSS calculation if not already present
                                            elif output_name == 'WOSS' and 'WOSS' not in desc and all(k in desc for k in ['hfc', 'norm_fdom', 'mag_voice_slope']):
                                                # Ensure we're using the plot_settings with the hfc_p95 value from precomputation
                                                # Initialize woss_params, can be from plot_settings or an empty dict
                                                woss_params = st.session_state.get('plot_settings', {}).copy()

                                                # Get HFC p95 from session state (set in Step 2)
                                                hfc_p95_session_value = st.session_state.get('hfc_p95')

                                                if hfc_p95_session_value is not None:
                                                    woss_params['hfc_p95'] = hfc_p95_session_value
                                                    logging.info(f"Using HFC p95 from session state for WOSS: {hfc_p95_session_value}")
                                                elif 'hfc_p95' not in woss_params:
                                                    # Fallback: if not in session_state AND not in plot_settings, calculate_woss has its own internal fallback
                                                    logging.warning("HFC p95 value not found in session state or plot_settings for WOSS. Relaying on calculate_woss fallback.")

                                                # Log the parameters being used for WOSS calculation
                                                logging.info(f"WOSS calculation parameters: hfc_p95={woss_params.get('hfc_p95', 'Not set/Fallback in calculate_woss')}, epsilon={woss_params.get('epsilon', 1e-4)}")
                                                woss = calculate_woss(desc, woss_params)
                                                values_dict['values'].extend(woss)
                                            # Regular case (for 1D arrays)
                                            elif isinstance(desc[internal_key], np.ndarray) and desc[internal_key].ndim == 1:
                                                values_dict['values'].extend(desc[internal_key])

                                    # Calculate statistics for each descriptor
                                    for desc_name, desc_stats in stats.items():
                                        if desc_stats['values']:
                                            values = np.array(desc_stats['values'])
                                            desc_stats['min'] = np.min(values)
                                            desc_stats['max'] = np.max(values)
                                            desc_stats['p5'] = np.percentile(values, 5)
                                            desc_stats['p90'] = np.percentile(values, 90)
                                        else:
                                            desc_stats['min'] = 'N/A'
                                            desc_stats['max'] = 'N/A'
                                            desc_stats['p5'] = 'N/A'
                                            desc_stats['p90'] = 'N/A'

                                    # Display the statistics in an expander
                                    with st.expander("📊 Descriptor Statistics Summary", expanded=True):
                                        st.markdown("### Signal Descriptor Statistics")
                                        st.markdown("The following statistics have been calculated for the selected traces:")

                                        # Group descriptors into categories for better organization
                                        categories = {
                                            "Primary Descriptors": [
                                                "Slope Magnitude", "Normalized HFC", "Normalized Dominant Frequencies",
                                                "WOSS", "Normalized Dominant Frequency", "Mag*Voice Slope"
                                            ],
                                            "Spectral Properties": [
                                                "Spectral Slope", "Spectral Bandwidth", "Spectral Rolloff",
                                                "Spectral Decrease", "Magnitude * Voice"
                                            ],
                                            "Signal Characteristics": [
                                                "Input Signal", "HFC", "Magnitude Spectrogram"
                                            ],
                                            "Spectrogram Statistics": [
                                                "Magnitude Spectrogram_max", "Magnitude Spectrogram_median", "Magnitude Spectrogram_std",
                                                "Magnitude * Voice_max", "Magnitude * Voice_median", "Magnitude * Voice_std"
                                            ],
                                            "Other Descriptors": []  # For any remaining descriptors
                                        }

                                        # Assign each descriptor to a category
                                        categorized_stats = {cat: [] for cat in categories.keys()}

                                        for desc_name, desc_stats in stats.items():
                                            if not desc_stats['values']:  # Skip empty descriptors
                                                continue

                                            # Find which category this descriptor belongs to
                                            assigned = False
                                            for cat_name, cat_descriptors in categories.items():
                                                if desc_name in cat_descriptors:
                                                    if isinstance(desc_stats['min'], (int, float)):
                                                        categorized_stats[cat_name].append({
                                                            "Descriptor": desc_name,
                                                            "Min": desc_stats['min'],  # Keep as numeric
                                                            "Max": desc_stats['max'],  # Keep as numeric
                                                            "P5": desc_stats['p5'],    # Keep as numeric
                                                            "P90": desc_stats['p90']   # Keep as numeric
                                                        })
                                                    else:
                                                        categorized_stats[cat_name].append({
                                                            "Descriptor": desc_name,
                                                            "Min": desc_stats['min'],
                                                            "Max": desc_stats['max'],
                                                            "P5": desc_stats['p5'],
                                                            "P90": desc_stats['p90']
                                                        })
                                                    assigned = True
                                                    break

                                            # If not assigned to any specific category, put in "Other Descriptors"
                                            if not assigned and desc_stats['values']:
                                                if isinstance(desc_stats['min'], (int, float)):
                                                    categorized_stats["Other Descriptors"].append({
                                                        "Descriptor": desc_name,
                                                        "Min": desc_stats['min'],  # Keep as numeric
                                                        "Max": desc_stats['max'],  # Keep as numeric
                                                        "P5": desc_stats['p5'],    # Keep as numeric
                                                        "P90": desc_stats['p90']   # Keep as numeric
                                                    })
                                                else:
                                                    categorized_stats["Other Descriptors"].append({
                                                        "Descriptor": desc_name,
                                                        "Min": desc_stats['min'],
                                                        "Max": desc_stats['max'],
                                                        "P5": desc_stats['p5'],
                                                        "P90": desc_stats['p90']
                                                    })

                                        # Display tables by category
                                        for cat_name, cat_stats in categorized_stats.items():
                                            if cat_stats:  # Only show categories with data
                                                st.subheader(cat_name)
                                                # Create DataFrame with numeric values
                                                df = pd.DataFrame(cat_stats)

                                                # Format the DataFrame for display with 4 decimal places
                                                # but keep the underlying data as numeric
                                                formatted_df = df.style.format({
                                                    "Min": "{:.4f}",
                                                    "Max": "{:.4f}",
                                                    "P5": "{:.4f}",
                                                    "P90": "{:.4f}"
                                                }, na_rep="N/A")

                                                st.table(formatted_df)

                                        # Add explanation
                                        st.markdown("""
                                        **Note:**
                                        - **Min/Max**: Absolute minimum and maximum values across all traces
                                        - **P5**: 5th percentile value (95% of values are above this)
                                        - **P90**: 90th percentile value (10% of values are above this)
                                        """)

                                    # Store the statistics in session state for later use
                                    st.session_state.descriptor_statistics = stats

                                # Proceed to results - only show this button after descriptors are calculated
                                if st.session_state.analysis_complete:
                                    st.markdown("---")
                                    st.success("✅ Descriptor calculation is complete for AOI. You can now view the detailed results.")
                                    if st.button("View Results", key="view_results_button_aoi", help="Open the detailed analysis report"):
                                        st.session_state.current_step = "view_results"
                                        st.rerun()

                            except Exception as e:
                                st.error(f"Error calculating descriptors for AOI: {e}")
                                logging.error(f"Descriptor calculation failed for AOI: {e}", exc_info=True)

            except Exception as e:
                st.error(f"Error finding traces within AOI: {e}")
                logging.error(f"AOI trace finding failed: {e}", exc_info=True)

    # Single Inline Mode
    elif st.session_state.selection_mode == "Single inline (all crosslines)":
        st.subheader("Selected Area Information")
        # Use selected_inline_number if available, otherwise use selected_inline
        selected_inline = st.session_state.selected_inline_number if 'selected_inline_number' in st.session_state else st.session_state.selected_inline
        st.write(f"Selected inline: {selected_inline}")
        st.write(f"Number of selected traces: {len(st.session_state.selected_indices)}")

        # Check if we have loaded trace data
        loaded_trace_data = st.session_state.get('loaded_trace_data', [])

        if not loaded_trace_data:
            # Load trace data for the selected inline
            with st.spinner(f"Loading traces for inline {selected_inline}..."):
                loaded_trace_data = []
                max_len = 0
                try:
                    for trace_idx in st.session_state.selected_indices:
                        # Load the trace sample
                        trace_sample = load_trace_sample(st.session_state.header_loader.source_file_path, trace_idx)
                        loaded_trace_data.append({'trace_sample': trace_sample, 'trace_idx': trace_idx})
                        max_len = max(max_len, len(trace_sample))

                    # Pad traces if necessary
                    if max_len > 0:
                        for item in loaded_trace_data:
                            if len(item['trace_sample']) < max_len:
                                pad_width = max_len - len(item['trace_sample'])
                                item['trace_sample'] = np.pad(item['trace_sample'], (0, pad_width), 'constant')

                    st.session_state.loaded_trace_data = loaded_trace_data
                    st.success(f"Successfully loaded {len(loaded_trace_data)} traces for inline {selected_inline}.")
                except Exception as e:
                    st.error(f"Error loading traces: {e}")
                    logging.error(f"Error loading traces for inline {selected_inline}: {e}", exc_info=True)
        else:
            st.success(f"Loaded {len(loaded_trace_data)} traces for analysis.")

        # Add a button to calculate descriptors
        st.markdown("---")
        st.markdown("### Next Steps:")
        st.markdown("1. Click **Calculate Descriptors** to process the selected traces and compute signal descriptors")
        st.markdown("2. Review the statistical summary that will appear")
        st.markdown("3. Click **View Results** to proceed to the results page for visualization and analysis options")

        if not st.session_state.get('GPU_AVAILABLE', False):
            st.error("GPU processing is required for this analysis mode, but no GPU is available. Please check your system configuration.")
        elif st.button("Calculate Descriptors", key="calculate_descriptors_button_inline", help="Process the selected traces and compute spectral descriptors"):
            if not loaded_trace_data:
                st.warning("No traces loaded to calculate descriptors.")
                return

            # Stack traces into a 2D array
            trace_samples = [item['trace_sample'] for item in loaded_trace_data]
            max_len = max(len(trace) for trace in trace_samples)
            padded_traces = [np.pad(trace, (0, max_len - len(trace)), 'constant') for trace in trace_samples]
            traces_array = np.stack(padded_traces).astype(np.float32)

            # Get descriptor settings from plot_settings
            descriptor_settings = {
                'use_band_limited': st.session_state.plot_settings.get('use_band_limited', False),
                'shape': st.session_state.plot_settings.get('shape', 0.35),
                'kmax': st.session_state.plot_settings.get('kmax', 120.0),
                'int_val': st.session_state.plot_settings.get('int_val', 35.0),
                'b1': st.session_state.plot_settings.get('b1', 5.0),
                'b2': st.session_state.plot_settings.get('b2', 40.0),
                'p_bandwidth': st.session_state.plot_settings.get('p_bandwidth', 2.0),
                'roll_percent': st.session_state.plot_settings.get('roll_percent', 0.80),
            }

            # Calculate descriptors using GPU
            progress_bar = st.progress(0)
            status_text = st.empty()
            status_text.text("Initializing descriptor calculation...")

            with st.spinner("Calculating spectral descriptors using GPU..."):
                try:
                    # Use the 2D chunked GPU function
                    fmax_calc = max_len // 2 if max_len > 0 else 250
                    calculated_descriptors_dict = dlogst_spec_descriptor_gpu_2d_chunked(
                        traces_array,
                        st.session_state.dt,
                        fmax=fmax_calc,
                        batch_size=st.session_state.get('batch_size', 512),
                        **descriptor_settings
                    )

                    # Calculate WOSS if needed
                    if 'hfc' in calculated_descriptors_dict and 'norm_fdom' in calculated_descriptors_dict and 'mag_voice_slope' in calculated_descriptors_dict:
                        # Get HFC p95 from session state
                        hfc_p95 = st.session_state.get('hfc_p95', 1.0)

                        # Create WOSS parameters
                        woss_params = {
                            'hfc_p95': hfc_p95,
                            'epsilon': st.session_state.plot_settings.get('epsilon', 1e-4),
                            'fdom_exponent': st.session_state.plot_settings.get('fdom_exponent', 2.0)
                        }

                        # Calculate WOSS for each trace
                        woss_array = np.zeros_like(calculated_descriptors_dict['hfc'])
                        for i in range(traces_array.shape[0]):
                            trace_components = {
                                'hfc': calculated_descriptors_dict['hfc'][i],
                                'norm_fdom': calculated_descriptors_dict['norm_fdom'][i],
                                'mag_voice_slope': calculated_descriptors_dict['mag_voice_slope'][i]
                            }
                            woss_array[i] = calculate_woss(trace_components, woss_params)

                        # Add WOSS to the calculated descriptors
                        calculated_descriptors_dict['WOSS'] = woss_array

                    # Store the calculated descriptors in session state
                    st.session_state.calculated_descriptors = calculated_descriptors_dict
                    st.session_state.analysis_complete = True
                    st.success("Descriptor calculation complete.")

                    # Add a button to view results
                    st.markdown("---")
                    st.success("✅ Descriptor calculation is complete. You can now view the detailed results.")
                    if st.button("View Results", key="view_results_button_inline", help="Open the detailed analysis report"):
                        st.session_state.current_step = "view_results"
                        st.rerun()

                except Exception as e:
                    st.error(f"Error calculating descriptors: {e}")
                    logging.error(f"Error calculating descriptors for inline {selected_inline}: {e}", exc_info=True)
                    return

    # Single Crossline Mode
    if st.session_state.selection_mode == "Single crossline (all inlines)":
        st.subheader("Single Crossline Mode")
        # ... (rest of the Single Crossline Mode logic)
        st.info("Single Crossline Mode logic goes here.") # Placeholder

    # Polyline Mode
    if st.session_state.selection_mode == "By Polyline File Import":
        st.subheader("Polyline Mode")
        # ... (rest of the Polyline Mode logic)
        st.info("Polyline Mode logic goes here.") # Placeholder

    # Add a "Start New Analysis" button to the sidebar
    st.sidebar.markdown("---")
    if st.sidebar.button("🔄 Start New Analysis", use_container_width=True):
        reset_state()
        st.success("Starting new analysis. All temporary data has been cleared.")
        st.rerun()
