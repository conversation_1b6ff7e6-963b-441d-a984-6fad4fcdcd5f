"""
Simple test script to verify the fix for the logging issue in dlogst_spec_descriptor_gpu.py
"""

import numpy as np
import logging
import sys

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Import the GPU functions directly from the module
try:
    from utils.dlogst_spec_descriptor_gpu import dlogst_spec_descriptor_gpu_2d_chunked
    
    # Create some test data
    data = np.random.rand(10, 1000).astype(np.float32)
    dt = 0.004
    
    # Call the function with a specific batch size
    logging.info("Calling dlogst_spec_descriptor_gpu_2d_chunked with batch_size=5")
    result = dlogst_spec_descriptor_gpu_2d_chunked(
        data,
        dt,
        batch_size=5
    )
    
    print("Function call successful!")
    print(f"Result keys: {list(result.keys())}")
    logging.info("Function call successful!")
    logging.info(f"Result keys: {list(result.keys())}")
    
    # Test successful
    print("TEST PASSED: The function executed without the 'logging' variable error.")
    
except Exception as e:
    print(f"Error: {e}")
    logging.error(f"Error: {e}")
    exc_type, exc_value, exc_traceback = sys.exc_info()
    logging.error(f"Exception type: {exc_type}")
    logging.error(f"Exception value: {exc_value}")
    import traceback
    tb_str = '\n'.join(traceback.format_tb(exc_traceback))
    logging.error(f"Traceback: {tb_str}")
    print(tb_str)
    
    # Test failed
    print("TEST FAILED: The function encountered an error.")
