"""
Test script to verify the fix for the broadcasting issue in dlogst_spec_descriptor_gpu.py
"""

import numpy as np
import logging
import sys

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

try:
    # Import the GPU functions
    from utils.dlogst_spec_descriptor_gpu import dlogst_spec_descriptor_gpu_2d_chunked

    # Create some test data
    data = np.random.rand(10, 1000).astype(np.float32)
    dt = 0.004

    # Call the function with a specific batch size
    logging.info("Calling dlogst_spec_descriptor_gpu_2d_chunked with batch_size=5")
    result = dlogst_spec_descriptor_gpu_2d_chunked(
        data,
        dt,
        batch_size=5
    )

    logging.info("Function call successful!")
    logging.info(f"Result keys: {list(result.keys())}")
    
    # Check if spec_bandwidth is in the results
    if 'spec_bandwidth' in result:
        logging.info(f"spec_bandwidth shape: {result['spec_bandwidth'].shape}")
    
    logging.info("Test passed successfully!")

except Exception as e:
    logging.error(f"Error in test: {e}")
    exc_type, exc_value, exc_traceback = sys.exc_info()
    logging.error(f"Exception type: {exc_type}")
    logging.error(f"Exception value: {exc_value}")
    import traceback
    tb_str = '\n'.join(traceback.format_tb(exc_traceback))
    logging.error(f"Traceback: {tb_str}")
    sys.exit(1)
