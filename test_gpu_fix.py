"""
Streamlit app to test the fix for the logging issue in dlogst_spec_descriptor_gpu.py
This specifically tests the function that was causing the error.
"""

import streamlit as st
import torch
import numpy as np
import logging
import sys

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

st.title("GPU Function Fix Test")

# Initialize GPU functions
try:
    # Import app.py's GPU initialization function
    from app import initialize_gpu_functions, GPU_AVAILABLE
    
    if GPU_AVAILABLE:
        st.success("GPU functions imported successfully")
        
        # Get the function from app.py
        from app import dlogst_spec_descriptor_gpu_2d_chunked
        
        # Create test data
        data = np.random.rand(10, 1000).astype(np.float32)
        dt = 0.004
        
        # Test with explicit batch_size parameter
        if st.button("Test with batch_size parameter"):
            try:
                st.info("Testing with batch_size=5")
                result = dlogst_spec_descriptor_gpu_2d_chunked(
                    data,
                    dt,
                    batch_size=5
                )
                st.success("Test passed! Function executed successfully with batch_size parameter.")
                st.write(f"Result keys: {list(result.keys())}")
            except Exception as e:
                st.error(f"Test failed: {e}")
                exc_type, exc_value, exc_traceback = sys.exc_info()
                st.error(f"Exception type: {exc_type}")
                st.error(f"Exception value: {exc_value}")
                import traceback
                tb_str = '\n'.join(traceback.format_tb(exc_traceback))
                st.code(tb_str)
    else:
        st.error("GPU is not available")
        
except Exception as e:
    st.error(f"Error: {e}")
    exc_type, exc_value, exc_traceback = sys.exc_info()
    st.error(f"Exception type: {exc_type}")
    st.error(f"Exception value: {exc_value}")
    import traceback
    tb_str = '\n'.join(traceback.format_tb(exc_traceback))
    st.code(tb_str)
