"""
Test page for the Streamlit app to verify the fix for the logging issue in dlogst_spec_descriptor_gpu.py
"""

import streamlit as st
import numpy as np
import logging
import sys

def render():
    """Render the test page UI."""
    st.title("GPU Function Fix Test")

    # Check if GPU is available from session state
    GPU_AVAILABLE = st.session_state.get('GPU_AVAILABLE', False)

    if not GPU_AVAILABLE:
        st.error("GPU is not available. Cannot run the test.")
        return

    st.success("GPU is available")

    # Import the GPU functions directly
    try:
        from utils.dlogst_spec_descriptor_gpu import dlogst_spec_descriptor_gpu_2d_chunked
        st.success("GPU functions imported successfully")

        # Create test data
        data = np.random.rand(10, 1000).astype(np.float32)
        dt = 0.004

        # Test with explicit batch_size parameter
        if st.button("Test with batch_size parameter"):
            try:
                with st.spinner("Running test..."):
                    st.info("Testing with batch_size=5")
                    result = dlogst_spec_descriptor_gpu_2d_chunked(
                        data,
                        dt,
                        batch_size=5
                    )
                    st.success("Test passed! Function executed successfully with batch_size parameter.")
                    st.write(f"Result keys: {list(result.keys())}")
            except Exception as e:
                st.error(f"Test failed: {e}")
                exc_type, exc_value, exc_traceback = sys.exc_info()
                st.error(f"Exception type: {exc_type}")
                st.error(f"Exception value: {exc_value}")
                import traceback
                tb_str = '\n'.join(traceback.format_tb(exc_traceback))
                st.code(tb_str)

        # Test with auto batch_size (None)
        if st.button("Test with auto batch_size"):
            try:
                with st.spinner("Running test..."):
                    st.info("Testing with batch_size=None (auto)")
                    result = dlogst_spec_descriptor_gpu_2d_chunked(
                        data,
                        dt,
                        batch_size=None
                    )
                    st.success("Test passed! Function executed successfully with auto batch_size.")
                    st.write(f"Result keys: {list(result.keys())}")
            except Exception as e:
                st.error(f"Test failed: {e}")
                exc_type, exc_value, exc_traceback = sys.exc_info()
                st.error(f"Exception type: {exc_type}")
                st.error(f"Exception value: {exc_value}")
                import traceback
                tb_str = '\n'.join(traceback.format_tb(exc_traceback))
                st.code(tb_str)
    except Exception as e:
        st.error(f"Error importing GPU functions: {e}")
        exc_type, exc_value, exc_traceback = sys.exc_info()
        st.error(f"Exception type: {exc_type}")
        st.error(f"Exception value: {exc_value}")
        import traceback
        tb_str = '\n'.join(traceback.format_tb(exc_traceback))
        st.code(tb_str)

if __name__ == "__main__":
    render()
